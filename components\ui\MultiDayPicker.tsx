import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';

interface MultiDayPickerProps {
  selectedDays: string[];
  onDaysChange: (days: string[]) => void;
  isDark?: boolean;
  label?: string;
}

const weekDays = [
  { label: 'Domingo', value: '0' },
  { label: '<PERSON>nes', value: '1' },
  { label: 'Martes', value: '2' },
  { label: 'Miércoles', value: '3' },
  { label: 'Jueves', value: '4' },
  { label: 'Viernes', value: '5' },
  { label: 'Sábado', value: '6' }
];

const MultiDayPicker: React.FC<MultiDayPickerProps> = ({
  selectedDays,
  onDaysChange,
  isDark,
  label,
}) => {
  const toggleDay = (dayValue: string) => {
    const newSelectedDays = selectedDays.includes(dayValue)
      ? selectedDays.filter(day => day !== dayValue)
      : [...selectedDays, dayValue].sort();
    onDaysChange(newSelectedDays);
  };

  return (
    <View style={styles.container}>
      {label && (
        <Text style={[styles.label, isDark && styles.labelDark]}>
          {label}
        </Text>
      )}
      <View style={styles.daysContainer}>
        {weekDays.map((day) => (
          <TouchableOpacity
            key={day.value}
            style={[
              styles.dayButton,
              isDark && styles.dayButtonDark,
              selectedDays.includes(day.value) && styles.selectedDayButton,
              isDark && selectedDays.includes(day.value) && styles.selectedDayButtonDark,
            ]}
            onPress={() => toggleDay(day.value)}
          >
            <Text
              style={[
                styles.dayText,
                isDark && styles.dayTextDark,
                selectedDays.includes(day.value) && styles.selectedDayText,
              ]}
            >
              {day.label.substring(0, 3)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#000',
  },
  labelDark: {
    color: '#fff',
  },
  daysContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 8,
  },
  dayButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    borderWidth: 1,
    borderColor: '#ddd',
    minWidth: '30%',
    alignItems: 'center',
  },
  dayButtonDark: {
    backgroundColor: '#333',
    borderColor: '#444',
  },
  selectedDayButton: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  selectedDayButtonDark: {
    backgroundColor: '#0A84FF',
    borderColor: '#0A84FF',
  },
  dayText: {
    fontSize: 14,
    color: '#000',
  },
  dayTextDark: {
    color: '#fff',
  },
  selectedDayText: {
    color: '#fff',
    fontWeight: '600',
  },
});

export default MultiDayPicker;