import { Image, StyleSheet, TextInput, TouchableOpacity, ActivityIndicator, KeyboardAvoidingView, Platform, Modal, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Constants from 'expo-constants';
import { useState, useEffect } from 'react';
import { MaterialIcons } from '@expo/vector-icons';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from '@/context/AuthContext';
import { getApiUrl, setApiUrl, updateEndpoints, normalizeUrl } from '@/constants/Api';

export default function LoginScreen() {
  const { colorScheme } = useColorScheme();
  const isDevelopment = Constants.appOwnership === 'expo' || __DEV__;
  const defaultEmail = isDevelopment ? 'MESSI' : '';
  const defaultPassword = isDevelopment ? 'BARCELONA' : '';

  const [email, setEmail] = useState(defaultEmail);
  const [password, setPassword] = useState(defaultPassword);
  
  const { login, loading, error, clearError } = useAuth();

  // Add validation for login form
  const [isFormValid, setIsFormValid] = useState(false);

  // Validate form whenever inputs change
  useEffect(() => {
    setIsFormValid(email.trim().length > 0 && password.trim().length > 0);
  }, [email, password]);

  // Clear error when inputs change
  useEffect(() => {
    if (error) {
      clearError();
    }
  }, [email, password]);

  const [isConfigVisible, setIsConfigVisible] = useState(false);
  const [apiUrl, setApiUrlState] = useState('');

  useEffect(() => {
    // Cargar la URL inicial
    const loadApiUrl = async () => {
      const url = await getApiUrl();
      setApiUrlState(url);
      updateEndpoints(url);
      
      // Si no hay URL configurada, mostrar el modal automáticamente
      if (!url.trim()) {
        setIsConfigVisible(true);
      }
    };
    loadApiUrl();
  }, []);

  const handleSaveApiUrl = async () => {
    try {
      // Normalizar la URL antes de validar
      const normalizedUrl = normalizeUrl(apiUrl);
      
      // Validar que la URL sea válida
      new URL(normalizedUrl);
      
      await setApiUrl(normalizedUrl);
      updateEndpoints(normalizedUrl);
      setIsConfigVisible(false);
      Alert.alert('Éxito', 'URL del API actualizada correctamente');
    } catch (error) {
      Alert.alert('Error', 'Por favor ingrese una URL válida');
    }
  };

  const gradientColors = colorScheme === 'dark' 
    ? ['#2E3192', '#1BFFFF'] as const
    : ['#4158D0', '#C850C0'] as const;

  const handleLogin = async () => {
    if (!apiUrl.trim()) {
      Alert.alert(
        'Configuración Requerida',
        'Por favor configure la URL del API antes de iniciar sesión',
        [
          {
            text: 'Configurar',
            onPress: () => setIsConfigVisible(true)
          }
        ]
      );
      return;
    }
    
    try {
      new URL(apiUrl); // Validar que sea una URL válida
      await login(email, password);
    } catch (error) {
      Alert.alert(
        'Error de Configuración',
        'La URL del API no es válida. Por favor verifique la configuración.',
        [
          {
            text: 'Configurar',
            onPress: () => setIsConfigVisible(true)
          }
        ]
      );
    }
  };

  return (
    <LinearGradient 
      colors={gradientColors} 
      style={styles.container}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}>
      <TouchableOpacity 
        style={styles.configButton}
        onPress={() => setIsConfigVisible(true)}
      >
        <MaterialIcons 
          name="cloud-queue"  // Corregido: usando guión en lugar de guión bajo
          size={24} 
          color={colorScheme === 'dark' ? '#ECEDEE' : '#11181C'} 
        />
      </TouchableOpacity>

      {/* Modal de configuración */}
      <Modal
        visible={isConfigVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setIsConfigVisible(false)}
      >
        <ThemedView style={styles.modalContainer}>
          <ThemedView style={styles.modalContent}>
            <ThemedText style={styles.modalTitle}>Configuración API</ThemedText>
            <TextInput
              placeholder="URL del API"
              value={apiUrl}
              onChangeText={setApiUrlState}
              style={[
                styles.input,
                { color: colorScheme === 'dark' ? '#ECEDEE' : '#11181C' }
              ]}
              placeholderTextColor={colorScheme === 'dark' ? '#9BA1A6' : '#687076'}
            />
            <ThemedView style={styles.modalButtons}>
              <TouchableOpacity 
                onPress={() => setIsConfigVisible(false)}
                style={[styles.modalButton, styles.cancelButton]}
              >
                <ThemedText>Cancelar</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity 
                onPress={handleSaveApiUrl}
                style={[styles.modalButton, styles.saveButton]}
              >
                <ThemedText style={styles.saveButtonText}>Guardar</ThemedText>
              </TouchableOpacity>
            </ThemedView>
          </ThemedView>
        </ThemedView>
      </Modal>

      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}>
        <ThemedView style={styles.content}>
          <Image
            source={require('@/assets/images/login.png')}
            style={styles.logo}
            resizeMode="contain"
            accessibilityLabel="App logo"
          />
          
          <ThemedText 
            style={[
              styles.title, 
              { color: colorScheme === 'dark' ? '#FFFFFF' : '#000000' }
            ]}>
            Intelibots
          </ThemedText>
          
          <ThemedView style={styles.formContainer}>
            <TextInput
              placeholder="Username"
              defaultValue={defaultEmail}
              onChangeText={setEmail}
              placeholderTextColor={colorScheme === 'dark' ? '#9BA1A6' : '#687076'}
              style={[
                styles.input,
                { color: colorScheme === 'dark' ? '#ECEDEE' : '#11181C' }
              ]}
              autoCapitalize="none"
              returnKeyType="next"
              accessibilityLabel="Username input"
              testID="username-input"
            />
            <TextInput
              placeholder="Password"
              defaultValue={defaultPassword}
              onChangeText={setPassword}
              placeholderTextColor={colorScheme === 'dark' ? '#9BA1A6' : '#687076'}
              secureTextEntry
              style={[
                styles.input,
                { color: colorScheme === 'dark' ? '#ECEDEE' : '#11181C' }
              ]}
              returnKeyType="done"
              onSubmitEditing={isFormValid ? handleLogin : undefined}
              accessibilityLabel="Password input"
              testID="password-input"
            />
            
            {error ? (
              <ThemedView style={styles.errorContainer}>
                <ThemedText style={styles.errorText}>{error}</ThemedText>
              </ThemedView>
            ) : null}
            
            <TouchableOpacity 
              onPress={handleLogin}
              disabled={loading || !isFormValid}
              style={[
                styles.loginButton,
                { backgroundColor: colorScheme === 'dark' ? '#ECEDEE' : '#0a7ea4' },
                (!isFormValid && !loading) && styles.loginButtonDisabled
              ]}
              accessibilityLabel="Login button"
              testID="login-button"
            >
              {loading ? (
                <ActivityIndicator color={colorScheme === 'dark' ? '#151718' : '#FFFFFF'} />
              ) : (
                <ThemedText 
                  style={[
                    styles.loginButtonText,
                    { color: colorScheme === 'dark' ? '#151718' : '#FFFFFF' }
                  ]}>
                  Iniciar Sesión
                </ThemedText>
              )}
            </TouchableOpacity>
          </ThemedView>
        </ThemedView>
      </KeyboardAvoidingView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  logo: {
    width: 200,
    height: 120,
    marginBottom: 40,
  },
  formContainer: {
    width: '100%',
    maxWidth: 320,
    gap: 16,
  },
  input: {
    width: '100%',
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    fontSize: 16,
  },
  loginButton: {
    width: '100%',
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  loginButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    marginVertical: 10,
    padding: 10,
    borderRadius: 5,
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
    width: '100%',
  },
  errorText: {
    color: '#ff4444',
    textAlign: 'center',
  },
  loginButtonDisabled: {
    opacity: 0.5,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 24,
    paddingTop: 24,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.25)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
    letterSpacing: 1,
  },
  configButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 1,
    padding: 8,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    padding: 20,
    borderRadius: 10,
    backgroundColor: '#fff',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalButton: {
    padding: 10,
    borderRadius: 5,
    width: '45%',
  },
  cancelButton: {
    backgroundColor: '#e0e0e0',
  },
  saveButton: {
    backgroundColor: '#0a7ea4',
  },
  saveButtonText: {
    color: '#fff',
    textAlign: 'center',
  },
}); 
