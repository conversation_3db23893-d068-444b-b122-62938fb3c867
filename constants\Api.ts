import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';

const isDevelopment = Constants.appOwnership === 'expo' || __DEV__;
const DEFAULT_API_URL = isDevelopment ? 'https://devapps.intelisiscloud.com/chataiv2' : '';

// Función para obtener la URL actual
export const getApiUrl = async (): Promise<string> => {
  const storedUrl = await AsyncStorage.getItem('@ApiUrl');
  return storedUrl || DEFAULT_API_URL;
};

// Función para actualizar la URL
export const setApiUrl = async (url: string): Promise<void> => {
  await AsyncStorage.setItem('@ApiUrl', url);
};

// Función para normalizar URLs eliminando el slash final si existe
export const normalizeUrl = (url: string): string => {
  return url.replace(/\/+$/, '');
};

// Endpoints dinámicos
export const createEndpoints = (baseUrl: string) => ({
  LOGIN: `${baseUrl}/login`,
  BOTS: `${baseUrl}/bots`,
  HISTORY: `${baseUrl}/history`,
  QUERIES: `${baseUrl}/queries`,
  TRANSCRIPT: `${baseUrl}/transcript`,
  CRON: `${baseUrl}/cron`,
  DELETE_CRON: (taskId: string) => `${baseUrl}/cron/${taskId}`,
  REGISTER_PUSH_TOKEN: `${baseUrl}/register-push-token`,
});

// Export default endpoints (será actualizado cuando se cargue la URL)
export let ENDPOINTS = createEndpoints(DEFAULT_API_URL);

// Función para actualizar los endpoints
export const updateEndpoints = (newBaseUrl: string) => {
  ENDPOINTS = createEndpoints(newBaseUrl);
};
