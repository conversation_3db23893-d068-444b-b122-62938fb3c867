function parseCronExpression(cronExpression: string): string {
  const parts = cronExpression.split(' ');
  if (parts.length !== 5) return cronExpression;

  const [minutes, hours, dayOfMonth, month, dayOfWeek] = parts;

  // Formatear la hora
  const formatTime = (h: string, m: string) => {
    const hour = parseInt(h);
    const minute = parseInt(m);
    const period = hour >= 12 ? 'PM' : 'AM';
    const formattedHour = hour % 12 || 12;
    return `${formattedHour}:${minute.toString().padStart(2, '0')} ${period}`;
  };

  const time = formatTime(hours, minutes);

  // Días de la semana en español
  const weekDays = {
    '0': 'Domingo',
    '1': 'Lunes',
    '2': 'Martes',
    '3': 'Miércoles',
    '4': 'Jueves',
    '5': 'Viernes',
    '6': 'Sábado'
  };

  // Casos comunes
  if (dayOfMonth === '*' && month === '*') {
    // Diario
    if (dayOfWeek === '*') {
      return `Diario a las ${time}`;
    }
    
    // Días específicos de la semana
    if (dayOfWeek.includes(',')) {
      const days = dayOfWeek.split(',')
        .map(d => weekDays[d as keyof typeof weekDays])
        .join(' y ');
      return `${days} a las ${time}`;
    }
    
    // Un día específico de la semana
    if (dayOfWeek in weekDays) {
      return `Cada ${weekDays[dayOfWeek as keyof typeof weekDays]} a las ${time}`;
    }
  }

  // Día específico del mes
  if (dayOfMonth !== '*' && month === '*' && dayOfWeek === '*') {
    return `Día ${dayOfMonth} de cada mes a las ${time}`;
  }

  // Meses en español
  const months = {
    '1': 'Enero',
    '2': 'Febrero',
    '3': 'Marzo',
    '4': 'Abril',
    '5': 'Mayo',
    '6': 'Junio',
    '7': 'Julio',
    '8': 'Agosto',
    '9': 'Septiembre',
    '10': 'Octubre',
    '11': 'Noviembre',
    '12': 'Diciembre'
  };

  // Fecha específica del año
  if (dayOfMonth !== '*' && month !== '*' && dayOfWeek === '*') {
    return `${dayOfMonth} de ${months[month as keyof typeof months]} a las ${time}`;
  }

  // Si no coincide con ningún patrón común, devolver la expresión original
  return cronExpression;
}

export { parseCronExpression };