// DataChart.styles.ts
import { StyleSheet, Dimensions } from "react-native";

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: "transparent",
    paddingTop: 40,
  },
  scrollContainer: {
    flex: 1,
    paddingTop: 24,
  },
  contentContainer: {
    flexGrow: 1,
    paddingTop: 24,
    paddingBottom: 40,
  },
  scrollContentContainer: {
    flexGrow: 1,
    paddingTop: 40,
    paddingBottom: 40,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 24,
    marginHorizontal: 16,
    marginTop: 20,
    paddingTop: 20,
  },
  chartTitleDark: {
    color: "#fff",
  },
  filterSection: {
    marginHorizontal: 16,
    marginBottom: 20,
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderRadius: 12,
    padding: 16,
  },
  chartScrollContainer: {
    marginTop: 16,
    marginBottom: 24,
  },
  chartContentContainer: {
    paddingHorizontal: 16,
    paddingLeft: 24,
  },
  chartContainer: {
    marginVertical: 10,
    paddingHorizontal: 5,
    backgroundColor: "transparent",
  },
  colorLegendContainer: {
    marginTop: 15,
    marginBottom: 10,
    paddingHorizontal: 20,
    flexDirection: "row",
    alignItems: "center",
  },
  colorLegendLabel: {
    fontSize: 14,
    fontWeight: "600",
    marginRight: 10,
    color: "#333",
  },
  colorLegendScale: {
    flex: 1,
  },
  colorLegendItems: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 5,
  },
  colorLegendItem: {
    alignItems: "center",
  },
  colorLegendSquare: {
    width: 16,
    height: 16,
    borderRadius: 2,
    borderWidth: 0.5,
    borderColor: "#e0e0e0",
  },
  colorLegendLabels: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  colorLegendText: {
    fontSize: 12,
    color: "#666",
  },
  container: {
    width: Dimensions.get("window").width - 32,
    borderRadius: 8,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "#e0e0e0",
    marginVertical: 8,
  },
  emptyContainer: {
    height: 50,
    justifyContent: "center",
    alignItems: "center",
  },
  text: {
    color: "#333",
  },
  textDark: {
    color: "#fff",
  },
  chart: {
    borderRadius: 8,
    paddingRight: 20,
    paddingLeft: 16,
    paddingVertical: 16,
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    marginVertical: 16,
    marginLeft: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 16,
    color: "#333",
  },
  filterItem: {
    marginBottom: 20,
  },
  filterLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: "#333",
    fontWeight: "500",
  },
  pickerButton: {
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#E0E0E0",
    borderRadius: 8,
    padding: 16,
    minHeight: 56,
    justifyContent: "center",
  },
  pickerButtonDark: {
    backgroundColor: "#2A2A2A",
    borderColor: "#404040",
  },
  pickerButtonText: {
    fontSize: 16,
    color: "#333",
  },
  pickerButtonTextDark: {
    color: "#fff",
  },
  resetButton: {
    backgroundColor: "#FF3B30",
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 8,
  },
  resetButtonDark: {
    backgroundColor: "#FF453A",
  },
  resetButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 14,
  },
  chartTypeButtons: {
    flexDirection: "row",
    gap: 8,
  },
  chartTypeButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: "#F5F5F5",
    alignItems: "center",
  },
  chartTypeButtonDark: {
    backgroundColor: "#2A2A2A",
  },
  chartTypeButtonActive: {
    backgroundColor: "#2196F3",
  },
  chartTypeText: {
    fontSize: 14,
    color: "#666",
    fontWeight: "500",
  },
  chartTypeTextDark: {
    color: "#fff",
  },
  chartTypeTextActive: {
    color: "#fff",
  },
  dataCountContainer: {
    marginVertical: 5,
  },
  dataCount: {
    fontSize: 12,
    color: "#666",
    fontStyle: "italic",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#fff",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: "70%",
  },
  modalContentDark: {
    backgroundColor: "#1a1a1a",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  modalHeaderText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  modalHeaderTextDark: {
    color: "#fff",
  },
  modalCloseText: {
    fontSize: 16,
    color: "#007AFF",
  },
  modalCloseTextDark: {
    color: "#0A84FF",
  },
  modalScrollView: {
    maxHeight: "60%",
  },
  modalItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  modalItemDark: {
    borderBottomColor: "#333",
  },
  modalItemSelected: {
    backgroundColor: "#f0f0f0",
  },
  modalItemSelectedDark: {
    backgroundColor: "#2a2a2a",
  },
  modalItemText: {
    fontSize: 16,
    color: "#333",
  },
  modalItemTextDark: {
    color: "#fff",
  },
  modalItemTextSelected: {
    fontWeight: "600",
    color: "#007AFF",
  },
  metricButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#cccccc",
    marginRight: 8,
    backgroundColor: "#f0f0f0",
  },
  metricButtonActive: {
    backgroundColor: "#2196F3",
    borderColor: "#2196F3",
  },
  metricButtonText: {
    color: "#333",
  },
  metricButtonTextActive: {
    color: "#fff",
  },
  comparisonHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  comparisonToggle: {
    backgroundColor: '#e0e0e0',
    padding: 8,
    borderRadius: 4,
  },
  comparisonToggleActive: {
    backgroundColor: '#2196F3',
  },
  comparisonToggleDark: {
    backgroundColor: '#333',
  },
  comparisonToggleText: {
    color: '#333',
    fontSize: 14,
  },
  comparisonToggleTextActive: {
    color: '#fff',
  },
});

export default styles;
