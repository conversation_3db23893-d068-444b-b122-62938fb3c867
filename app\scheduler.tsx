import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import SchedulerForm from '@/components/SchedulerForm';

export default function SchedulerScreen() {
  const { messageId, messageText, botId } = useLocalSearchParams();

  return (
    <ThemedView style={styles.container}>
      <View style={styles.messagePreview}>
        <ThemedText style={styles.previewLabel}>Mensaje a programar:</ThemedText>
        <ThemedText style={styles.messageText}>{messageText}</ThemedText>
      </View>
      <SchedulerForm 
        messageText={messageText as string}
        botId={botId as string}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  messagePreview: {
    marginBottom: 20,
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  previewLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  messageText: {
    fontSize: 16,
  },
});
