import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { useEffect, useState } from 'react';
import 'react-native-reanimated';
import { MaterialIcons } from '@expo/vector-icons';
import { TouchableOpacity } from 'react-native';
import * as Notifications from 'expo-notifications';

import { useColorScheme } from '@/hooks/useColorScheme';
import { ProfileDrawer } from '@/components/ProfileDrawer';
import { AuthProvider } from '@/context/AuthContext';
import { ChatProvider } from '@/context/ChatContext';
import { FavoritesProvider } from '@/context/FavoritesContext';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const { colorScheme } = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });
  const [isProfileVisible, setIsProfileVisible] = useState(false);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  useEffect(() => {
    const subscription = Notifications.addNotificationReceivedListener(notification => {
      const data = notification.request.content.data;
      // Manejar la notificación recibida
      console.log('Notificación recibida:', data);
    });

    const responseSubscription = Notifications.addNotificationResponseReceivedListener(response => {
      const data = response.notification.request.content.data;
      // Manejar cuando el usuario toca la notificación
      console.log('Notificación tocada:', data);
    });

    return () => {
      subscription.remove();
      responseSubscription.remove();
    };
  }, []);

  if (!loaded) {
    return null;
  }

  const headerRight = () => (
    <TouchableOpacity
      onPress={() => setIsProfileVisible(true)}
      style={{ marginRight: 15 }}
    >
      <MaterialIcons
        name="person"
        size={24}
        color={colorScheme === 'dark' ? '#ECEDEE' : '#11181C'}
      />
    </TouchableOpacity>
  );

  return (
    <AuthProvider>
      <ChatProvider>
        <FavoritesProvider>
          <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
            <Stack
              screenOptions={{
                headerStyle: {
                  backgroundColor: colorScheme === 'dark' ? '#151718' : '#fff',
                },
                headerTintColor: colorScheme === 'dark' ? '#ECEDEE' : '#11181C',
                headerRight,
              }}
            >
              <Stack.Screen
                name="index"
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="bots"
                options={{ 
                  title: 'Bots Disponibles',
                  headerBackTitle: 'Volver',
                  headerRight,
                }}
              />
              <Stack.Screen
                name="chat/[id]"
                options={{ 
                  title: 'Chat',
                  headerBackTitle: 'Bots',
                  headerRight,
                }}
              />
              <Stack.Screen
                name="chart"
                options={{ 
                  title: 'Data Visualization',
                  headerBackTitle: 'Back',
                  headerRight,
                }}
              />
              <Stack.Screen
                name="favorites"
                options={{ 
                  title: 'Favoritos',
                  headerBackTitle: 'Volver',
                  headerRight,
                }}
              />
              <Stack.Screen
                name="scheduler"
                options={{ 
                  title: 'Programar Mensaje',
                  headerBackTitle: 'Volver',
                  headerRight,
                }}
              />
              <Stack.Screen
                name="scheduled"
                options={{ 
                  title: 'Tareas Programadas',
                  headerBackTitle: 'Volver',
                  headerRight,
                }}
              />
            </Stack>
            <ProfileDrawer 
              isVisible={isProfileVisible}
              onClose={() => setIsProfileVisible(false)}
            />
            <StatusBar style="auto" />
          </ThemeProvider>
        </FavoritesProvider>
      </ChatProvider>
    </AuthProvider>
  );
} 
