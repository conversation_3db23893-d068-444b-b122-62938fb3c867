import { useColorScheme as useNativeColorScheme } from 'react-native';
import { create } from 'zustand';

type Theme = 'light' | 'dark' | null;

type ThemeStore = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
};

const useThemeStore = create<ThemeStore>((set) => ({
  theme: null,
  setTheme: (theme: Theme) => set({ theme }),
}));

export function useColorScheme() {
  const systemTheme = useNativeColorScheme();
  const { theme, setTheme } = useThemeStore();
  
  return {
    colorScheme: theme ?? systemTheme ?? 'light',
    setColorScheme: setTheme,
  };
}
