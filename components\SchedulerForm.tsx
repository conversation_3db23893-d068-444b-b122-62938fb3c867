import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Button,
  Platform,
  Alert
} from 'react-native';
import { router } from 'expo-router';
import DateTimePicker from '@react-native-community/datetimepicker';
import CustomPicker from '@/components/ui/CustomPicker';
import MultiDayPicker from '@/components/ui/MultiDayPicker';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from '@/context/AuthContext';
import { ENDPOINTS } from '@/constants/Api';
import { getBotCategoryName } from '@/services/ChatService';

interface SchedulerFormProps {
  messageText: string;
  botId: string;
}

const scheduleOptions = [
  { label: 'Diario', value: 'daily' },
  { label: 'Semanal', value: 'weekly' },
  { label: 'Mensual', value: 'monthly' },
  { label: 'Anual', value: 'yearly' },
  { label: 'Días Específicos', value: 'specific' }
];

const weekDays = [
  { label: 'Domingo', value: '0' },
  { label: 'Lunes', value: '1' },
  { label: 'Martes', value: '2' },
  { label: 'Miércoles', value: '3' },
  { label: 'Jueves', value: '4' },
  { label: 'Viernes', value: '5' },
  { label: 'Sábado', value: '6' }
];

const months = [
  { label: 'Enero', value: 1 },
  { label: 'Febrero', value: 2 },
  { label: 'Marzo', value: 3 },
  { label: 'Abril', value: 4 },
  { label: 'Mayo', value: 5 },
  { label: 'Junio', value: 6 },
  { label: 'Julio', value: 7 },
  { label: 'Agosto', value: 8 },
  { label: 'Septiembre', value: 9 },
  { label: 'Octubre', value: 10 },
  { label: 'Noviembre', value: 11 },
  { label: 'Diciembre', value: 12 }
];

const communicationOptions = [
  { label: 'Notificación', value: 'notification' }
];

const SchedulerForm = ({ messageText, botId }: SchedulerFormProps) => {
  const { colorScheme } = useColorScheme();
  const { authData } = useAuth();
  const isDark = colorScheme === 'dark';
  const [frequency, setFrequency] = useState('daily');
  const [selectedDay, setSelectedDay] = useState(1);
  const [selectedMonth, setSelectedMonth] = useState(1);
  const [selectedWeek, setSelectedWeek] = useState('1');
  const [time, setTime] = useState(new Date());
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [selectedDays, setSelectedDays] = useState<string[]>([]);
  const [communicationMethod, setCommunicationMethod] = useState('notification');

  const onTimeChange = (event: any, selectedTime: Date | undefined) => {
    setShowTimePicker(Platform.OS === 'ios');
    if (selectedTime) {
      setTime(selectedTime);
    }
  };

  const handleSaveSchedule = async () => {
    let cronExpression = '';
    switch (frequency) {
      case 'daily':
        cronExpression = `${time.getMinutes()} ${time.getHours()} * * *`;
        break;
      case 'weekly':
        cronExpression = `${time.getMinutes()} ${time.getHours()} * * ${selectedWeek}`;
        break;
      case 'monthly':
        cronExpression = `${time.getMinutes()} ${time.getHours()} ${selectedDay} * *`;
        break;
      case 'yearly':
        cronExpression = `${time.getMinutes()} ${time.getHours()} ${selectedDay} ${selectedMonth} *`;
        break;
      case 'specific':
        if (selectedDays.length === 0) {
          Alert.alert('Error', 'Debe seleccionar al menos un día');
          return;
        }
        cronExpression = `${time.getMinutes()} ${time.getHours()} * * ${selectedDays.join(',')}`;
        break;
      default:
        break;
    }

    try {
      if (!authData?.access_token) {
        Alert.alert(
          'Error',
          'No hay una sesión activa. Por favor, inicie sesión nuevamente.'
        );
        return;
      }

      const botName = getBotCategoryName(Number(botId));
      
      const formData = new FormData();
      formData.append('frequency', cronExpression);
      formData.append('message', messageText);
      formData.append('bot', botName);
      formData.append('email', '<EMAIL>');

      const response = await fetch(ENDPOINTS.CRON, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authData.access_token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Error al programar la tarea');
      }

      Alert.alert(
        'Éxito',
        'La tarea ha sido programada correctamente',
        [{ text: 'OK', onPress: () => router.back() }]
      );

    } catch (error) {
      console.error('Error al guardar la programación:', error);
      Alert.alert(
        'Error',
        'No se pudo programar la tarea. Por favor, intenta de nuevo.'
      );
    }
  };

  return (
    <View style={styles.container}>
      <Text style={[styles.title, isDark && styles.textDark]}>Programar Tarea</Text>

      <View style={styles.group}>
        <CustomPicker
          label="Medio de Comunicación"
          selectedValue={communicationMethod}
          onValueChange={(value) => setCommunicationMethod(value.toString())}
          items={communicationOptions}
          isDark={isDark}
        />
      </View>

      <View style={styles.group}>
        <CustomPicker
          label="Frecuencia"
          selectedValue={frequency}
          onValueChange={(value) => setFrequency(value.toString())}
          items={scheduleOptions}
          isDark={isDark}
        />
      </View>

      {(frequency === 'monthly' || frequency === 'yearly') && (
        <View style={styles.group}>
          <CustomPicker
            label="Día del Mes"
            selectedValue={selectedDay}
            onValueChange={(value) => setSelectedDay(Number(value))}
            items={Array.from({ length: 31 }, (_, i) => ({
              label: (i + 1).toString(),
              value: i + 1
            }))}
            isDark={isDark}
          />
          {selectedDay === 31 && (
            <Text style={styles.warning}>
              Nota: Se ejecutará el último día del mes si el mes no tiene 31 días.
            </Text>
          )}
        </View>
      )}

      {frequency === 'yearly' && (
        <View style={styles.group}>
          <CustomPicker
            label="Mes"
            selectedValue={selectedMonth}
            onValueChange={(value) => setSelectedMonth(Number(value))}
            items={months}
            isDark={isDark}
          />
        </View>
      )}

      {frequency === 'weekly' && (
        <View style={styles.group}>
          <CustomPicker
            label="Día de la Semana"
            selectedValue={selectedWeek}
            onValueChange={(value) => setSelectedWeek(value.toString())}
            items={weekDays}
            isDark={isDark}
          />
        </View>
      )}

      {frequency === 'specific' && (
        <View style={styles.group}>
          <MultiDayPicker
            label="Seleccionar días"
            selectedDays={selectedDays}
            onDaysChange={setSelectedDays}
            isDark={isDark}
          />
        </View>
      )}

      <View style={styles.group}>
        <Text style={[styles.label, isDark && styles.textDark]}>Hora:</Text>
        {Platform.OS === 'ios' ? (
          <DateTimePicker
            value={time}
            mode="time"
            display="spinner"
            onChange={onTimeChange}
          />
        ) : (
          <View>
            <Button
              title={`Seleccionar hora: ${time.toLocaleTimeString()}`}
              onPress={() => setShowTimePicker(true)}
            />
            {showTimePicker && (
              <DateTimePicker
                value={time}
                mode="time"
                display="default"
                onChange={onTimeChange}
              />
            )}
          </View>
        )}
      </View>

      <Button title="Guardar Programación" onPress={handleSaveSchedule} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  group: {
    marginVertical: 10,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#000',
  },
  label: {
    marginBottom: 5,
    fontSize: 16,
    color: '#000',
  },
  textDark: {
    color: '#fff',
  },
  warning: {
    color: 'red',
    fontSize: 12,
    marginTop: 5,
  },
});

export default SchedulerForm;
