import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
} from "react";
import {
  ChatMessage,
  fetchChatHistory,
  sendMessage,
} from "@/services/ChatService";
import { useAuth } from "./AuthContext";

interface ChatContextType {
  histories: Record<number, ChatMessage[]>;
  loadingHistories: Record<number, boolean>;
  currentBotId: number | null;
  sendingMessage: boolean;
  fetchHistoryForBot: (botId: number) => Promise<ChatMessage[] | undefined>;
  setCurrentBot: (botId: number | null) => void;
  addMessageToHistory: (botId: number, message: ChatMessage) => void;
  sendMessageToBot: (   
    botId: number,
    messageText: string
  ) => Promise<ChatMessage | null>;
}

const ChatContext = createContext<ChatContextType>({
  histories: {},
  loadingHistories: {},
  currentBotId: null,
  sendingMessage: false,
  fetchHistoryForBot: async () => undefined,
  setCurrentBot: () => {},
  addMessageToHistory: () => {},
  sendMessageToBot: async () => null,
});

export const ChatProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [histories, setHistories] = useState<Record<number, ChatMessage[]>>({});
  const [loadingHistories, setLoadingHistories] = useState<Record<number, boolean>>({});
  const [currentBotId, setCurrentBotId] = useState<number | null>(null);
  const [sendingMessage, setSendingMessage] = useState<boolean>(false);
  const { authData } = useAuth();

  const fetchHistoryForBot = async (botId: number) => {
    if (!authData?.access_token) {
      console.error("No auth token available for fetching chat history");
      return;
    }

    try {
      // Marcar la carga de la historia de este bot
      setLoadingHistories((prev) => ({ ...prev, [botId]: true }));

      console.log(`Fetching history for bot ${botId}`);
      const history = await fetchChatHistory(botId, authData.access_token);
      console.log(`Received ${history.length} messages for bot ${botId}`);
      // Guardar la historia en el estado
      setHistories((prev) => ({ ...prev, [botId]: history }));
      return history;
    } catch (e) {
      console.error(`Error fetching history for bot ${botId}:`, e);
      // Evitar intentos repetidos de fetch
      setHistories((prev) => ({ ...prev, [botId]: [] }));
      throw e;
    } finally {
      setLoadingHistories((prev) => ({ ...prev, [botId]: false }));
    }
  };

  const setCurrentBot = (botId: number | null) => {
    setCurrentBotId(botId);
  };

  const addMessageToHistory = (botId: number, message: ChatMessage) => {
    setHistories((prev) => {
      const botHistory = prev[botId] || [];
      return {
        ...prev,
        [botId]: [...botHistory, message],
      };
    });
  };

  const sendMessageToBot = async (
    botId: number,
    messageText: string
  ): Promise<ChatMessage | null> => {
    if (!authData?.access_token) {
      console.error("No auth token available for sending message");
      return null;
    }

    try {
      setSendingMessage(true);

      // Obtenemos el user ID de los datos
      const userId = authData.user.Usuario;

      // Creamos un mensaje temporal del usuario para mostrarlo inmediatamente
      const tempUserMessage: ChatMessage = {
        ID: Date.now(),
        RID: Date.now(),
        Usuario: userId,
        UsuarioDestino: "",
        Mensaje: messageText,
        FechaRegistro: new Date().toISOString(),
        MensajeTipo: "Texto",
        Respuesta: null,
      };

      // Agregamos el mensaje del usuario a la historia
      addMessageToHistory(botId, tempUserMessage);

      console.log(`Sending message to API: ${messageText}`);
      // Se pasa el proveedor seleccionado al llamar a sendMessage
      const response = await sendMessage(
        botId,
        messageText,
        authData.access_token,
        userId
      );

      if (response) {
        console.log("Received response from bot:", response);
        // Validar la respuesta recibida
        const isValidResponse =
          response.Respuesta !== null &&
          response.Respuesta !== undefined &&
          response.Respuesta !== "Sin respuesta";

        if (isValidResponse) {
          // Actualizar el mensaje temporal con la respuesta
          const updatedMessage: ChatMessage = {
            ...tempUserMessage,
            Respuesta: response.Respuesta,
            MensajeTipo: response.MensajeTipo,
          };

          // Reemplazar el mensaje temporal con el actualizado
          setHistories((prev) => {
            const botHistory = [...(prev[botId] || [])];
            const tempIndex = botHistory.findIndex(
              (msg) =>
                msg.ID === tempUserMessage.ID &&
                msg.RID === tempUserMessage.RID
            );
            if (tempIndex >= 0) {
              botHistory[tempIndex] = updatedMessage;
            } else {
              botHistory.push(updatedMessage);
            }
            return {
              ...prev,
              [botId]: botHistory,
            };
          });
          return updatedMessage;
        } else {
          console.log("Response was not valid, keeping only the user message");
          return tempUserMessage;
        }
      } else {
        console.log(
          "No response received from bot, keeping only the user message"
        );
        return tempUserMessage;
      }
    } catch (e) {
      console.error(`Error sending message to bot ${botId}:`, e);
      return null;
    } finally {
      setSendingMessage(false);
    }
  };

  return (
    <ChatContext.Provider
      value={{
        histories,
        loadingHistories,
        currentBotId,
        sendingMessage,
        fetchHistoryForBot,
        setCurrentBot,
        addMessageToHistory,
        sendMessageToBot,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

// Hook para acceder al ChatContext
export const useChat = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error("useChat must be used within a ChatProvider");
  }
  return context;
};
