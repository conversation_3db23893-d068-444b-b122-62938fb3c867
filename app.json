{"expo": {"name": "intelibots", "displayName": "Intelibots", "icon": "./assets/icon.png", "newArchEnabled": true, "extra": {"eas": {"projectId": "ac14dcbe-c102-43e8-b8fe-44cdc71ed33c"}}, "plugins": [["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#ffffff", "enableBackgroundRemoteNotifications": true}]], "ios": {"bundleIdentifier": "com.intelisis.intelibots", "infoPlist": {"NSMicrophoneUsageDescription": "La aplicación requiere el uso del micrófono para la función de Voz a Texto.", "ITSAppUsesNonExemptEncryption": false, "UIBackgroundModes": ["remote-notification"], "NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true}}, "supportsTablet": true}, "android": {"permissions": ["NOTIFICATIONS", "RECORD_AUDIO"], "package": "com.intelisis.intelibots", "googleServicesFile": "./google-services.json", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "config": {"usesCleartextTraffic": true}}}}