import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Text, Dimensions } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';

interface DataTableProps {
  data: any[];
}

export const DataTable: React.FC<DataTableProps> = ({ data }) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [processedData, setProcessedData] = useState<any[]>([]);
  const [headers, setHeaders] = useState<string[]>([]);
  const screenWidth = Dimensions.get('window').width;
  
  // Lista de campos que no deben ser formateados con separadores de miles
  const NON_FORMATTED_FIELDS = ['Año', 'Ano', 'Anio', 'Year', 'Ejercicio', 'ID'];

  // Función auxiliar para formatear números
  const formatNumber = (value: number, fieldName: string): string => {
    // Si el campo está en la lista de exclusión, retornar el número sin formato
    if (NON_FORMATTED_FIELDS.some(field => fieldName.includes(field))) {
      return value.toString();
    }

    // Si es un número entero, no mostrar decimales
    if (Number.isInteger(value)) {
      return value.toLocaleString('es-MX');
    }
    // Si tiene decimales, mostrar 2 decimales
    return value.toLocaleString('es-MX', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  // Process the data to handle nested objects and ensure consistent structure
  useEffect(() => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      console.log('DataTable received invalid data:', data);
      setProcessedData([]);
      setHeaders([]);
      return;
    }
    
    console.log('Processing table data:', JSON.stringify(data.slice(0, 2), null, 2));
    
    // Collect all possible keys from all objects
    const allKeys = new Set<string>();
    data.forEach(item => {
      if (item && typeof item === 'object') {
        Object.keys(item).forEach(key => allKeys.add(key));
      }
    });
    
    const keysList = Array.from(allKeys);
    setHeaders(keysList);
    
    // Process each row to flatten nested objects and ensure all keys exist
    const processed = data.map(item => {
      const row: Record<string, string> = {};
      
      // Initialize all keys with empty strings
      keysList.forEach(key => {
        row[key] = '';
      });
      
      // Fill in values for keys that exist in this item
      if (item && typeof item === 'object') {
        Object.entries(item).forEach(([key, value]) => {
          if (value === null || value === undefined) {
            row[key] = '';
          } else if (typeof value === 'object') {
            // Para objetos Date, formatearlos
            if (value instanceof Date || (typeof value === 'object' && value.toString().includes('T00:00:00'))) {
              try {
                const dateStr = value.toString();
                if (dateStr.includes('T')) {
                  const date = new Date(dateStr);
                  row[key] = date.toLocaleDateString('es-MX');
                } else {
                  row[key] = dateStr;
                }
              } catch (e) {
                try {
                  row[key] = JSON.stringify(value);
                } catch (e) {
                  row[key] = '[Objeto Complejo]';
                }
              }
            } else {
              try {
                row[key] = JSON.stringify(value);
              } catch (e) {
                row[key] = '[Objeto Complejo]';
              }
            }
          } else {
            // Formatear números con el formato mexicano
            if (typeof value === 'number') {
              row[key] = formatNumber(value, key);
            } else {
              row[key] = String(value);
            }
          }
        });
      }
      
      return row;
    });
    
    console.log(`Processed ${processed.length} rows with ${keysList.length} columns`);
    setProcessedData(processed);
  }, [data]);

  // If no data or empty array, show a message
  if (!processedData || processedData.length === 0 || headers.length === 0) {
    return (
      <View style={[styles.container, styles.emptyContainer]}>
        <Text style={[styles.text, isDark && styles.textDark]}>No data available</Text>
      </View>
    );
  }

  // Calculate column width based on number of columns and screen width
  const minColumnWidth = 100;
  const calculatedWidth = Math.max(minColumnWidth, (screenWidth * 0.7) / headers.length);
  const columnWidth = Math.min(calculatedWidth, 150); // Cap at 150px max

  // Calculate table height based on number of rows (with a minimum and maximum)
  const rowHeight = 40; // Approximate height of each row
  const headerHeight = 40; // Approximate height of the header
  const minTableHeight = 100; // Minimum table height
  const maxTableHeight = 200; // Maximum table height
  const calculatedTableHeight = Math.min(
    maxTableHeight,
    Math.max(minTableHeight, headerHeight + processedData.length * rowHeight)
  );

  return (
    <View style={styles.container}>
      {/* Row count indicator */}
      <View style={[styles.rowCountContainer, isDark && styles.rowCountContainerDark]}>
        <Text style={[styles.rowCountText, isDark && styles.rowCountTextDark]}>
          {processedData.length} {processedData.length === 1 ? 'row' : 'rows'}
        </Text>
      </View>
      
      <ScrollView horizontal={true} showsHorizontalScrollIndicator={true}>
        <View>
          {/* Table Header */}
          <View style={styles.headerRow}>
            {headers.map((header, index) => (
              <View 
                key={`header-${index}`} 
                style={[
                  styles.headerCell, 
                  isDark && styles.headerCellDark,
                  index === headers.length - 1 && styles.lastCell,
                  { width: columnWidth }
                ]}
              >
                <Text style={[styles.headerText, isDark && styles.headerTextDark]} numberOfLines={1}>
                  {header}
                </Text>
              </View>
            ))}
          </View>

          {/* Table Body - Scrollable */}
          <ScrollView 
            style={[styles.tableBody, { maxHeight: calculatedTableHeight }]} 
            showsVerticalScrollIndicator={true} 
            nestedScrollEnabled={true}
          >
            {processedData.map((row, rowIndex) => (
              <View 
                key={`row-${rowIndex}`} 
                style={[
                  styles.row, 
                  isDark && styles.rowDark,
                  rowIndex % 2 === 0 && styles.evenRow,
                  rowIndex % 2 === 0 && isDark && styles.evenRowDark
                ]}
              >
                {headers.map((header, colIndex) => (
                  <View 
                    key={`cell-${rowIndex}-${colIndex}`} 
                    style={[
                      styles.cell, 
                      isDark && styles.cellDark,
                      colIndex === headers.length - 1 && styles.lastCell,
                      { width: columnWidth }
                    ]}
                  >
                    <Text 
                      style={[styles.text, isDark && styles.textDark]} 
                      numberOfLines={2}
                      ellipsizeMode="tail"
                    >
                      {row[header] || ''}
                    </Text>
                  </View>
                ))}
              </View>
            ))}
          </ScrollView>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    maxHeight: 250,
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    marginVertical: 8,
  },
  emptyContainer: {
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rowCountContainer: {
    padding: 4,
    backgroundColor: '#eee',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    alignItems: 'center',
  },
  rowCountContainerDark: {
    backgroundColor: '#222',
    borderBottomColor: '#444',
  },
  rowCountText: {
    fontSize: 12,
    color: '#666',
  },
  rowCountTextDark: {
    color: '#aaa',
  },
  headerRow: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
  },
  headerRowDark: {
    backgroundColor: '#333',
  },
  headerCell: {
    padding: 8,
    borderRightWidth: 1,
    borderRightColor: '#e0e0e0',
    backgroundColor: '#f5f5f5',
  },
  headerCellDark: {
    borderRightColor: '#444',
    backgroundColor: '#333',
  },
  headerText: {
    fontWeight: 'bold',
    color: '#333',
  },
  headerTextDark: {
    color: '#fff',
  },
  tableBody: {
    maxHeight: 150,
  },
  row: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  rowDark: {
    borderTopColor: '#444',
  },
  evenRow: {
    backgroundColor: '#fafafa',
  },
  evenRowDark: {
    backgroundColor: '#2a2a2a',
  },
  cell: {
    padding: 8,
    borderRightWidth: 1,
    borderRightColor: '#e0e0e0',
  },
  cellDark: {
    borderRightColor: '#444',
  },
  lastCell: {
    borderRightWidth: 0,
  },
  text: {
    color: '#333',
  },
  textDark: {
    color: '#fff',
  },
});
