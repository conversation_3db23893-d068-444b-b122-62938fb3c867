import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
import { ENDPOINTS } from '@/constants/Api';
import { registerForPushNotificationsAsync, sendPushTokenToServer } from '@/services/NotificationService';

// Define types for our authentication data
export interface User {
  Usuario: string;
  Nombre: string;
  Sucursal: number;
}

export interface Sucursal {
  Sucursal: number;
  SucursalNombre: string;
}

export interface Empresa {
  Empresa: string;
  Sucursales: Sucursal[];
}

export interface AuthData {
  user: User;
  empresas: Empresa[];
  access_token: string;
  refresh_token: string;
}

interface AuthContextType {
  authData: AuthData | null;
  loading: boolean;
  error: string | null;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  clearError: () => void;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  authData: null,
  loading: false,
  error: null,
  login: async () => {},
  logout: () => {},
  clearError: () => {},
});

// Provider component
export const AuthProvider: React.FC<{children: ReactNode}> = ({ children }) => {
  const [authData, setAuthData] = useState<AuthData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check if user is already logged in on mount
  useEffect(() => {
    const loadStoredAuthData = async () => {
      try {
        const storedAuthData = await AsyncStorage.getItem('@AuthData');
        if (storedAuthData) {
          setAuthData(JSON.parse(storedAuthData));
        }
      } catch (e) {
        console.error('Failed to load auth data from storage', e);
      } finally {
        setLoading(false);
      }
    };

    loadStoredAuthData();
  }, []);

  const login = async (username: string, password: string) => {
    try {
      setLoading(true);
      setError(null);
      
      // Create FormData object
      const formData = new FormData();
      formData.append("user", username);
      formData.append("password", password);
      formData.append("grant_type", "password");
      
      console.log('Attempting login with:', { username });
      
      const response = await fetch(ENDPOINTS.LOGIN, {
        method: 'POST',
        body: formData,
      });
      
      const responseText = await response.text();
      console.log('Response status:', response.status);
      console.log('Response text:', responseText);
      
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Error parsing response:', parseError);
        throw new Error(`Invalid response format: ${responseText.substring(0, 100)}`);
      }
      
      if (!response.ok) {
        throw new Error(data.detail || `Login failed with status ${response.status}`);
      }
      
      if (data.status === 'success') {
        const authDataToStore = data.data;
        console.log('Login successful, user:', authDataToStore.user.Nombre);
        
        // Store auth data in state and AsyncStorage
        setAuthData(authDataToStore);
        await AsyncStorage.setItem('@AuthData', JSON.stringify(authDataToStore));
        
        // Navigate to bots screen
        router.replace('/bots');
        
        // Registrar para notificaciones después del login exitoso
        const pushToken = await registerForPushNotificationsAsync();
        if (pushToken) {
          await sendPushTokenToServer(pushToken, authDataToStore.user.Usuario, authDataToStore.access_token);
        }
      } else {
        throw new Error(`Unexpected response format: ${JSON.stringify(data).substring(0, 100)}`);
      }
    } catch (e) {
      console.error('Login error:', e);
      setError(e instanceof Error ? e.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      // Clear auth data from state and storage
      setAuthData(null);
      await AsyncStorage.removeItem('@AuthData');
      
      // Navigate back to login
      router.replace('/');
    } catch (e) {
      console.error('Logout error', e);
    }
  };

  const clearError = () => {
    setError(null);
  };

  return (
    <AuthContext.Provider 
      value={{ 
        authData, 
        loading, 
        error, 
        login, 
        logout,
        clearError
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
