import React from 'react';
import { View, Text as RNText } from 'react-native';
import { Svg, Rect, Text } from 'react-native-svg';

interface Dataset {
  data: number[];
  color?: () => string;
  strokeWidth?: number;
}

interface GroupedBarChartData {
  labels: string[];
  datasets: Dataset[];
  legend: string[];
}

interface GroupedBarChartProps {
  data: GroupedBarChartData;
  width: number;
  height: number;
  chartConfig: {
    backgroundColor: string;
    backgroundGradientFrom: string;
    backgroundGradientTo: string;
    decimalPlaces: number;
    color: (opacity?: number) => string;
    labelColor: (opacity?: number) => string;
  };
  style?: any;
  horizontalLabelRotation?: number;
  showValuesOnTopOfBars?: boolean;
}

const GroupedBarChart: React.FC<GroupedBarChartProps> = ({ data, width, height, chartConfig, style, horizontalLabelRotation }) => {
  const padding = 20;
  const axisHeight = 20; // extra space for x-axis labels
  const legendHeight = 20; // extra space for legend
  const topPadding = 20; // extra padding at the top for labels
  const bottomPadding = padding + axisHeight; 
  const chartHeight = height - topPadding - bottomPadding - legendHeight; // drawing area for bars
  
  const isDark = chartConfig.backgroundColor && chartConfig.backgroundColor.toLowerCase() !== '#ffffff';
  const labelBackgroundFill = isDark ? 'rgba(0,0,0,0.8)' : 'rgba(255,255,255,0.8)';

  // Get max value among all datasets
  const allData = data.datasets.flatMap(ds => ds.data);
  const maxValue = Math.max(...allData, 0);

  // Determine bar width: for each label, we have data.datasets.length bars
  const groupCount = data.labels.length;
  const barsPerGroup = data.datasets.length;
  const numberOfBars = groupCount * barsPerGroup;
  const groupSpacing = 10; 
  const barSpacing = 5;
  const totalBarSpacing = (groupCount - 1) * groupSpacing + numberOfBars * barSpacing;
  const barWidth = (width - 2 * padding - totalBarSpacing) / numberOfBars;

  // Function to format number with 2 decimal places and thousands separator (Mexican format)
  const formatNumber = (num: number): string => {
    // Format with 2 decimal places
    const fixedNum = Number(num.toFixed(2));
    // Convert to string with thousands separator (comma)
    return fixedNum.toLocaleString('es-MX');
  };

  return (
    <View style={style}>
      <Svg width={width} height={height + 15}>
        {/* Renderizar barras y etiquetas numéricas */}
        {data.labels.map((label, groupIndex) => {
          const groupX = padding + groupIndex * (barsPerGroup * (barWidth + barSpacing) + groupSpacing);
          return data.datasets.map((ds, barIndex) => {
            const value = ds.data[groupIndex];
            const formattedValue = formatNumber(value);
            const barHeight = maxValue === 0 ? 0 : (value / maxValue) * chartHeight;
            const x = groupX + barIndex * (barWidth + barSpacing);
            const y = topPadding + (height - legendHeight - topPadding) - bottomPadding - barHeight;
            const fill = ds.color ? ds.color() : chartConfig.color(1);
            const labelY = Math.max(topPadding, y - 5);
            return (
              <React.Fragment key={`group-${groupIndex}-bar-${barIndex}`}> 
                <Rect x={x} y={y} width={barWidth} height={barHeight} fill={fill} />
                {/* Fondo para la etiqueta */}
                <Rect 
                  x={x + barWidth / 2 - 15}
                  y={labelY - 8}
                  width={30}
                  height={12}
                  fill={labelBackgroundFill}
                  rx={4}
                />
                {/* Etiqueta numérica */}
                <Text
                  x={x + barWidth / 2}
                  y={labelY}
                  fontSize="10"
                  fill={chartConfig.color(1)}
                  textAnchor="middle"
                  fontWeight="bold"
                >
                  {formattedValue}
                </Text>
              </React.Fragment>
            );
          });
        })}
        {/* Renderizar etiquetas del eje X */}
        {data.labels.map((label, groupIndex) => {
          const groupX = padding + groupIndex * (barsPerGroup * (barWidth + barSpacing) + groupSpacing);
          const groupBlockWidth = barsPerGroup * (barWidth + barSpacing) - barSpacing;
          const centerX = groupX + groupBlockWidth / 2;
          const labelY = (height - legendHeight) - 5;
          return (
            <Text
              key={`group-label-${groupIndex}`}
              x={centerX}
              y={labelY}
              fontSize="10"
              fill={chartConfig.labelColor(1)}
              textAnchor="middle"
              transform={horizontalLabelRotation && horizontalLabelRotation !== 0 ? `rotate(${horizontalLabelRotation}, ${centerX}, ${labelY})` : undefined}
            >
              {label}
            </Text>
          );
        })}
      </Svg>
      {/* Renderizar la leyenda debajo del gráfico */}
      <View style={{ flexDirection: 'row', justifyContent: 'center', marginTop: 2, height: legendHeight }}>
        {data.legend.map((metric, idx) => {
          const color = data.datasets[idx] && data.datasets[idx].color ? data.datasets[idx].color() : chartConfig.color(1);
          return (
            <View key={`legend-${idx}`} style={{ flexDirection: 'row', alignItems: 'center', marginHorizontal: 5 }}>
              <View style={{ width: 10, height: 10, backgroundColor: color, marginRight: 3 }} />
              <RNText style={{ fontSize: 10, color: chartConfig.labelColor(1) }}>{metric}</RNText>
            </View>
          );
        })}
      </View>
    </View>
  );
};

export default GroupedBarChart; 
