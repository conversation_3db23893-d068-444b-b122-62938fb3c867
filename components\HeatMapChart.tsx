import React from 'react';
import { View, Text as RNText } from 'react-native';
import { Svg, Rect, Text, Defs, LinearGradient, Stop } from 'react-native-svg';

// Helper function to generate sample data (similar to the example code provided)
export const generateData = (count: number, { min, max }: { min: number, max: number }) => {
  const result = [];
  for (let i = 0; i < count; i++) {
    const x = 'w' + (i + 1);
    const y = Math.floor(Math.random() * (max - min + 1)) + min;
    result.push({
      x,
      y
    });
  }
  return result;
};

// Function to generate demo heat map data for testing
export const generateHeatMapData = () => {
  const metrics = ['Metric1', 'Metric2', 'Metric3', 'Metric4', 'Metric5', 
                   'Metric6', 'Metric7', 'Metric8', 'Metric9'];
  const weeks = Array.from({ length: 18 }, (_, i) => `w${i + 1}`);
  
  const data = metrics.map(() => {
    return weeks.map(() => Math.floor(Math.random() * 90));
  });
  
  return {
    data,
    xLabels: weeks,
    yLabels: metrics
  };
};

interface HeatMapChartData {
  data: number[][];
  xLabels: string[];
  yLabels: string[];
  colorScale?: (value: number) => string;
  minValue?: number;
  maxValue?: number;
}

interface HeatMapChartProps {
  data: HeatMapChartData;
  width: number;
  height: number;
  chartConfig: {
    backgroundColor: string;
    backgroundGradientFrom: string;
    backgroundGradientTo: string;
    decimalPlaces: number;
    color: (opacity?: number) => string;
    labelColor: (opacity?: number) => string;
  };
  style?: any;
  showValues?: boolean;
  title?: string;
}

const HeatMapChart: React.FC<HeatMapChartProps> = ({
  data,
  width,
  height,
  chartConfig,
  style,
  showValues = true,
  title,
}) => {
  const padding = { top: title ? 60 : 40, right: 20, bottom: 40, left: 70 };
  const chartWidth = width - padding.left - padding.right;
  const chartHeight = height - padding.top - padding.bottom;

  // Determine min and max values for color scaling
  const flatData = data.data.flat();
  const minValue = data.minValue !== undefined ? data.minValue : Math.min(...flatData);
  const maxValue = data.maxValue !== undefined ? data.maxValue : Math.max(...flatData);
  
  // Calculate cell dimensions
  const cellWidth = chartWidth / data.xLabels.length;
  const cellHeight = chartHeight / data.yLabels.length;

  // Generate heat map color based on value
  const getHeatMapColor = (value: number) => {
    if (data.colorScale) {
      return data.colorScale(value);
    }
    
    // Use a consistent blue color scheme like in the example
    if (minValue === maxValue) return '#008FFB'; // Default blue if all values are the same
    
    const normalizedValue = (value - minValue) / (maxValue - minValue);
    
    // Create a blue-only color scale from light to dark blue (similar to the example image)
    if (normalizedValue < 0.2) {
      return '#E6F2FF'; // Lightest blue (almost white)
    } else if (normalizedValue < 0.4) {
      return '#CCE5FF'; // Light blue
    } else if (normalizedValue < 0.6) {
      return '#99CCFF'; // Medium light blue
    } else if (normalizedValue < 0.8) {
      return '#66B2FF'; // Medium blue
    } else {
      return '#008FFB'; // Darker blue - using the same blue as in the example
    }
  };

  // Format number for display
  const formatNumber = (num: number): string => {
    return Number(num.toFixed(chartConfig.decimalPlaces)).toLocaleString();
  };

  return (
    <View style={[{ marginBottom: 20 }, style]}>
      {/* Title if provided */}
      {title && (
        <RNText
          style={{
            fontSize: 16,
            fontWeight: 'bold',
            color: chartConfig.labelColor(1),
            textAlign: 'left',
            marginBottom: 10,
            marginLeft: padding.left,
          }}
        >
          {title}
        </RNText>
      )}
      
      <Svg width={width} height={height}>
        {/* Color legend */}
        <Rect
          x={padding.left}
          y={padding.top - 20}
          width={chartWidth}
          height={10}
          fill="url(#gradient)"
        />
        
        <Text
          x={padding.left}
          y={padding.top - 25}
          fontSize="10"
          fill={chartConfig.labelColor(1)}
          textAnchor="start"
        >
          {formatNumber(minValue)}
        </Text>
        
        <Text
          x={padding.left + chartWidth}
          y={padding.top - 25}
          fontSize="10"
          fill={chartConfig.labelColor(1)}
          textAnchor="end"
        >
          {formatNumber(maxValue)}
        </Text>
        
        {/* Y-axis labels */}
        {data.yLabels.map((label, index) => (
          <Text
            key={`y-label-${index}`}
            x={padding.left - 10}
            y={padding.top + index * cellHeight + cellHeight / 2}
            fontSize="12"
            fontWeight="500"
            fill={chartConfig.labelColor(1)}
            textAnchor="end"
            alignmentBaseline="middle"
          >
            {label}
          </Text>
        ))}

        {/* X-axis labels */}
        {data.xLabels.map((label, index) => (
          <Text
            key={`x-label-${index}`}
            x={padding.left + index * cellWidth + cellWidth / 2}
            y={height - padding.bottom + 20}
            fontSize="12"
            fontWeight="500"
            fill={chartConfig.labelColor(1)}
            textAnchor="middle"
          >
            {label}
          </Text>
        ))}

        {/* Heat map cells */}
        {data.data.map((row, rowIndex) => (
          <React.Fragment key={`row-${rowIndex}`}>
            {row.map((value, colIndex) => {
              const cellColor = getHeatMapColor(value);
              const x = padding.left + colIndex * cellWidth;
              const y = padding.top + rowIndex * cellHeight;
              
              return (
                <React.Fragment key={`cell-${rowIndex}-${colIndex}`}>
                  <Rect
                    x={x}
                    y={y}
                    width={cellWidth}
                    height={cellHeight}
                    fill={cellColor}
                    stroke="#ffffff"
                    strokeWidth={1}
                  />
                  
                  {showValues && (
                    <Text
                      x={x + cellWidth / 2}
                      y={y + cellHeight / 2}
                      fontSize="9"
                      fill="#333333"
                      fontWeight="bold"
                      textAnchor="middle"
                      alignmentBaseline="middle"
                    >
                      {formatNumber(value)}
                    </Text>
                  )}
                </React.Fragment>
              );
            })}
          </React.Fragment>
        ))}
        
        {/* Gradient definition for legend */}
        <Defs>
          <LinearGradient id="gradient" x1="0" y1="0" x2="1" y2="0">
            <Stop offset="0%" stopColor={getHeatMapColor(minValue)} />
            <Stop offset="25%" stopColor={getHeatMapColor(minValue + (maxValue - minValue) * 0.25)} />
            <Stop offset="50%" stopColor={getHeatMapColor((minValue + maxValue) / 2)} />
            <Stop offset="75%" stopColor={getHeatMapColor(minValue + (maxValue - minValue) * 0.75)} />
            <Stop offset="100%" stopColor={getHeatMapColor(maxValue)} />
          </LinearGradient>
        </Defs>
      </Svg>
    </View>
  );
};

export default HeatMapChart;
