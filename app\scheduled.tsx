import React, { useEffect, useState } from 'react';
import { FlatList, StyleSheet, TouchableOpacity, Alert, View } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { useAuth } from '@/context/AuthContext';
import { ENDPOINTS } from '@/constants/Api';
import { useColorScheme } from '@/hooks/useColorScheme';
import { parseCronExpression } from '@/utils/cronParser';

interface ScheduledTask {
  id: string;
  usuario: string;
  periodicidad: string;
  mensaje: string;
  bot: string;
  correo_electronico: string;
  created_at: string;
}

export default function ScheduledScreen() {
  const [tasks, setTasks] = useState<ScheduledTask[]>([]);
  const { authData } = useAuth();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const fetchTasks = async () => {
    try {
      const response = await fetch(ENDPOINTS.CRON, {
        headers: {
          'Authorization': `Bearer ${authData?.access_token}`,
        },
      });
      
      const data = await response.json();
      if (data.status === 'success') {
        setTasks(data.tasks);
      }
    } catch (error) {
      console.error('Error fetching scheduled tasks:', error);
      Alert.alert('Error', 'No se pudieron cargar las tareas programadas');
    }
  };

  const deleteTask = async (taskId: string) => {
    try {
      const response = await fetch(ENDPOINTS.DELETE_CRON(taskId), {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authData?.access_token}`,
        },
      });
      
      const data = await response.json();
      if (data.status === 'success') {
        setTasks(tasks.filter(task => task.id !== taskId));
        Alert.alert('Éxito', 'Tarea eliminada correctamente');
      }
    } catch (error) {
      console.error('Error deleting task:', error);
      Alert.alert('Error', 'No se pudo eliminar la tarea');
    }
  };

  const handleDeleteTask = (taskId: string) => {
    Alert.alert(
      'Confirmar eliminación',
      '¿Estás seguro de que deseas eliminar esta tarea programada?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { text: 'Eliminar', style: 'destructive', onPress: () => deleteTask(taskId) }
      ]
    );
  };

  useEffect(() => {
    fetchTasks();
  }, []);

  const renderItem = ({ item }: { item: ScheduledTask }) => (
    <View style={[
      styles.taskItem,
      isDark && { borderBottomColor: 'rgba(255, 255, 255, 0.1)' }
    ]}>
      <View style={styles.taskContent}>
        <ThemedText style={styles.botName}>{item.bot}</ThemedText>
        <ThemedText numberOfLines={2} style={styles.messageText}>
          {item.mensaje}
        </ThemedText>
        <ThemedText style={styles.scheduleText}>
          {parseCronExpression(item.periodicidad)}
        </ThemedText>
        <ThemedText style={styles.dateText}>
          Creado el {new Date(item.created_at).toLocaleDateString()}
        </ThemedText>
      </View>
      <TouchableOpacity 
        onPress={() => handleDeleteTask(item.id)}
        style={styles.deleteButton}
      >
        <MaterialIcons 
          name="delete" 
          size={24} 
          color={isDark ? '#888' : '#666'} 
        />
      </TouchableOpacity>
    </View>
  );

  return (
    <ThemedView style={styles.container}>
      {tasks.length === 0 ? (
        <View style={styles.emptyContainer}>
          <ThemedText>No hay tareas programadas</ThemedText>
        </View>
      ) : (
        <FlatList
          data={tasks}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          style={styles.list}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  list: {
    flex: 1,
  },
  taskItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  taskContent: {
    flex: 1,
    marginRight: 16,
  },
  botName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  messageText: {
    fontSize: 14,
    marginBottom: 4,
  },
  scheduleText: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 2,
  },
  dateText: {
    fontSize: 12,
    opacity: 0.7,
  },
  deleteButton: {
    justifyContent: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
