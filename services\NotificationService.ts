import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { ENDPOINTS } from '@/constants/Api';

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

export async function registerForPushNotificationsAsync() {
  let token;

  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    
    if (finalStatus !== 'granted') {
      return null;
    }
    
    token = (await Notifications.getExpoPushTokenAsync({
      projectId: 'ac14dcbe-c102-43e8-b8fe-44cdc71ed33c' // Tu projectId de app.json
    })).data;
  }

  return token;
}

export async function sendPushTokenToServer(token: string, userId: string, accessToken: string) {
  try {
    const response = await fetch(ENDPOINTS.REGISTER_PUSH_TOKEN, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        token,
        userId,
        platform: Platform.OS
      })
    });

    console.log('Push token registration response:', await response.json());
    
    if (!response.ok) {
      throw new Error('Error registering push token');
    }
  } catch (error) {
    console.error('Error sending push token to server:', error);
  }
}