import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { useColorScheme } from "@/hooks/useColorScheme";

interface HeatMapGraphProps {
  verticalLabels: string[];
  horizontalLabels: string[];
  data: number[][];
}

const getColorForValue = (value: number) => {
  if (value === 0) return "#ebedf0"; // Sin actividad
  else if (value < 5) return "#c6e48b"; // Baja
  else if (value < 10) return "#7bc96f"; // Media-baja
  else if (value < 15) return "#239a3b"; // Media-alta
  return "#196127"; // Alta
};

const HeatMapGraph: React.FC<HeatMapGraphProps> = ({
  verticalLabels,
  horizontalLabels,
  data,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === "dark";
  const textColor = isDark ? "#fff" : "#000";

  return (
    <View style={styles.container}>
      {/* Fila de etiquetas horizontales */}
      <View style={styles.horizontalLabelsRow}>
        <View style={styles.emptyCell} />
        {horizontalLabels.map((label, index) => (
          <View key={index} style={styles.horizontalLabelCell}>
            <Text
              style={[
                styles.labelText,
                styles.horizontalLabelText,
                { color: textColor },
              ]}
            >
              {label}
            </Text>
          </View>
        ))}
      </View>
      {/* Renderizamos las filas del heatmap */}
      <View style={styles.gridRow}>
        {verticalLabels.map((rowLabel, rowIndex) => (
          <View key={rowIndex} style={styles.rowContainer}>
            <View style={styles.verticalLabelCell}>
              <Text style={[styles.labelText, { color: textColor }]}>
                {rowLabel}
              </Text>
            </View>
            <View style={styles.rowCells}>
              {data[rowIndex]?.map((value, colIndex) => (
                <TouchableOpacity
                  key={colIndex}
                  style={[
                    styles.cell,
                    { backgroundColor: getColorForValue(value) },
                  ]}
                  onPress={() =>
                    console.log(
                      `Fila: ${rowLabel}, Columna: ${horizontalLabels[colIndex]} => Valor: ${value}`
                    )
                  }
                />
              ))}
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
  },
  horizontalLabelsRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  emptyCell: {
    width: 40,
    height: 40,
  },
  horizontalLabelCell: {
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 4,
  },
  // Se ajusta el estilo para que tras la rotación el texto se vea centrado
  horizontalLabelText: {
    transform: [
      { rotate: "-45deg" },
      { translateX: 15 }, // Ajusta este valor según tus necesidades
      { translateY: 15 }, // Ajusta este valor según tus necesidades
    ],
    textAlign: "center",
  },
  labelText: {
    fontSize: 10,
  },
  gridRow: {
    flexDirection: "column",
  },
  rowContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  verticalLabelCell: {
    width: 80,
    height: 20,
    justifyContent: "center",
    alignItems: "flex-end",
    marginRight: 4,
  },
  rowCells: {
    flexDirection: "row",
  },
  cell: {
    width: 20,
    height: 20,
    marginRight: 4,
    borderRadius: 3,
  },
});

export default HeatMapGraph;
