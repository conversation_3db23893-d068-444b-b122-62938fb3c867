import React, { createContext, useState, useContext, ReactNode, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface FavoriteMessage {
  id: string;
  text: string;
  timestamp: Date;
  botId: number;
}

interface FavoritesContextType {
  favorites: FavoriteMessage[];
  addFavorite: (message: FavoriteMessage) => Promise<void>;
  removeFavorite: (messageId: string) => Promise<void>;
  isFavorite: (messageId: string) => boolean;
}

const FavoritesContext = createContext<FavoritesContextType>({
  favorites: [],
  addFavorite: async () => {},
  removeFavorite: async () => {},
  isFavorite: () => false,
});

const STORAGE_KEY = '@favorites';

export const FavoritesProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [favorites, setFavorites] = useState<FavoriteMessage[]>([]);

  // <PERSON>gar favoritos guardados al iniciar
  useEffect(() => {
    const loadFavorites = async () => {
      try {
        const stored = await AsyncStorage.getItem(STORAGE_KEY);
        if (stored) {
          const parsedFavorites = JSON.parse(stored);
          // Convertir las fechas string a objetos Date
          const favoritesWithDates = parsedFavorites.map((fav: any) => ({
            ...fav,
            timestamp: new Date(fav.timestamp)
          }));
          setFavorites(favoritesWithDates);
        }
      } catch (error) {
        console.error('Error loading favorites:', error);
      }
    };

    loadFavorites();
  }, []);

  const addFavorite = async (message: FavoriteMessage) => {
    try {
      const newFavorites = [...favorites, message];
      setFavorites(newFavorites);
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(newFavorites));
    } catch (error) {
      console.error('Error saving favorite:', error);
    }
  };

  const removeFavorite = async (messageId: string) => {
    try {
      const newFavorites = favorites.filter(msg => msg.id !== messageId);
      setFavorites(newFavorites);
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(newFavorites));
    } catch (error) {
      console.error('Error removing favorite:', error);
    }
  };

  const isFavorite = (messageId: string) => {
    return favorites.some(msg => msg.id === messageId);
  };

  return (
    <FavoritesContext.Provider value={{
      favorites,
      addFavorite,
      removeFavorite,
      isFavorite,
    }}>
      {children}
    </FavoritesContext.Provider>
  );
};

export const useFavorites = () => {
  const context = useContext(FavoritesContext);
  if (!context) {
    throw new Error('useFavorites must be used within a FavoritesProvider');
  }
  return context;
};