import React from 'react';
import { FlatList, StyleSheet, TouchableOpacity, View } from 'react-native';
import { useRouter } from 'expo-router';
import { MaterialIcons } from '@expo/vector-icons';

import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { useFavorites } from '@/context/FavoritesContext';
import { useColorScheme } from '@/hooks/useColorScheme';

interface FavoriteMessage {
  id: string;
  text: string;
  timestamp: Date;
  botId: number;
}

export default function FavoritesScreen() {
  const { favorites, removeFavorite } = useFavorites();
  const router = useRouter();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const navigateToChat = (botId: number) => {
    router.push({
      pathname: '/chat/[id]',
      params: { id: botId.toString() }
    });
  };

  const renderItem = ({ item }: { item: FavoriteMessage }) => (
    <View style={[
      styles.favoriteItem,
      isDark && { borderBottomColor: 'rgba(255, 255, 255, 0.1)' }
    ]}>
      <TouchableOpacity 
        style={styles.favoriteContent}
        onPress={() => navigateToChat(item.botId)}
      >
        <ThemedText numberOfLines={2} style={styles.favoriteText}>
          {item.text}
        </ThemedText>
        <ThemedText style={styles.timestampText}>
          {new Date(item.timestamp).toLocaleDateString()}
        </ThemedText>
      </TouchableOpacity>
      <TouchableOpacity 
        onPress={() => removeFavorite(item.id)}
        style={styles.removeButton}
      >
        <MaterialIcons 
          name="delete" 
          size={24} 
          color={isDark ? '#888' : '#666'} 
        />
      </TouchableOpacity>
    </View>
  );

  return (
    <ThemedView style={styles.container}>
      {favorites.length === 0 ? (
        <View style={styles.emptyContainer}>
          <ThemedText>No hay mensajes favoritos</ThemedText>
        </View>
      ) : (
        <FlatList
          data={favorites}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          style={styles.list}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  list: {
    flex: 1,
  },
  favoriteItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  favoriteContent: {
    flex: 1,
    marginRight: 16,
  },
  favoriteText: {
    fontSize: 16,
    marginBottom: 8,
  },
  timestampText: {
    fontSize: 12,
    opacity: 0.7,
  },
  removeButton: {
    justifyContent: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});