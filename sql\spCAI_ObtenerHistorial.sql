CREATE PROCEDURE spCAI_ObtenerHistorial  
    @ID INT,  
    @Usuario NVARCHAR(50)  
AS  
BEGIN  
    SET NOCOUNT ON  
  
    SELECT   
        actual.ID,   
        actual.RID,   
        actual.Usuario,   
        actual.<PERSON>uario<PERSON><PERSON><PERSON>,   
        actual.<PERSON><PERSON><PERSON>,   
        actual.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,   
        actual.MensajeTipo,  
        (SELECT TOP 1 siguiente.Mensaje   
         FROM UsuarioChatD siguiente   
         WHERE siguiente.RID > actual.RID   
           AND (siguiente.Usuario = @Usuario OR siguiente.UsuarioDestino = @Usuario)   
         ORDER BY siguiente.RID ASC) AS Respuesta  
    FROM UsuarioChatD actual  
    WHERE actual.ID = @ID   
      AND (actual.Usuario = @Usuario)  
    ORDER BY actual.RID ASC  
END  