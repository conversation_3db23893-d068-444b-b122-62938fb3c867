import React, { useRef, useEffect } from "react";
import {
  Image,
  StyleSheet,
  Switch,
  TouchableOpacity,
  Animated,
  Dimensions,
  Platform,
  StatusBar,
  StyleSheet as RNStyleSheet,
} from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useAuth } from "@/context/AuthContext";
import { useChat } from "@/context/ChatContext";
import { useRouter } from 'expo-router';
import { MaterialIcons } from '@expo/vector-icons';

interface ProfileDrawerProps {
  isVisible: boolean;
  onClose: () => void;
}

const DRAWER_WIDTH = Dimensions.get("window").width * 0.8;
const TOP_INSET = Platform.OS === "ios" ? 50 : StatusBar.currentHeight || 0;

export function ProfileDrawer({ isVisible, onClose }: ProfileDrawerProps) {
  const { colorScheme, setColorScheme } = useColorScheme();
  const { authData, logout } = useAuth();
  const isDark = colorScheme === "dark";
  const translateX = useRef(new Animated.Value(DRAWER_WIDTH)).current;
  const backdropOpacity = useRef(new Animated.Value(0)).current;
  const router = useRouter();

  useEffect(() => {
    if (isVisible) {
      // Animar la aparición del drawer y el backdrop
      Animated.parallel([
        Animated.timing(translateX, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Animar la desaparición del drawer y el backdrop
      Animated.parallel([
        Animated.timing(translateX, {
          toValue: DRAWER_WIDTH,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isVisible, translateX, backdropOpacity]);

  const handleLogout = () => {
    onClose();
    logout();
  };

  const toggleTheme = () => {
    setColorScheme(isDark ? "light" : "dark");
  };

  const navigateToFavorites = () => {
    onClose();
    router.push('/favorites');
  };

  return (
    <Animated.View
      style={[
        styles.overlay,
        { opacity: backdropOpacity, pointerEvents: isVisible ? "auto" : "none" },
      ]}
    >
      <Animated.View style={[styles.backdrop, { opacity: backdropOpacity }]}>
        <TouchableOpacity
          style={RNStyleSheet.absoluteFill}
          onPress={onClose}
          activeOpacity={1}
        />
      </Animated.View>

      <Animated.View
        style={[
          styles.drawer,
          { transform: [{ translateX }] },
          { backgroundColor: isDark ? "#151718" : "#fff" },
          { paddingTop: TOP_INSET },
        ]}
      >
        <ThemedView style={styles.content}>
          <ThemedView style={styles.header}>
            <ThemedText style={styles.title}>Perfil</ThemedText>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <ThemedText style={styles.closeButtonText}>✕</ThemedText>
            </TouchableOpacity>
          </ThemedView>

          <ThemedView style={styles.userInfo}>
            <Image
              source={require("@/assets/images/icon.png")}
              style={styles.avatar}
            />
            <ThemedText style={styles.name}>
              {authData?.user?.Nombre || "Usuario Invitado"}
            </ThemedText>
            <ThemedText style={styles.email}>
              {authData?.user?.Usuario || "No has iniciado sesión"}
            </ThemedText>
          </ThemedView>

          {/* Sección para el cambio de Modo Oscuro */}
          <ThemedView style={styles.settingsSection}>
            <ThemedView style={styles.settingRow}>
              <ThemedText>Modo Oscuro</ThemedText>
              <Switch
                value={isDark}
                onValueChange={toggleTheme}
                trackColor={{ false: "#767577", true: "#81b0ff" }}
                thumbColor={isDark ? "#f5dd4b" : "#f4f3f4"}
              />
            </ThemedView>
          </ThemedView>

          {/* Botón de Favoritos */}
          <TouchableOpacity 
            style={styles.drawerItem} 
            onPress={navigateToFavorites}
          >
            <MaterialIcons 
              name="star" 
              size={24} 
              color={isDark ? '#ECEDEE' : '#11181C'} 
            />
            <ThemedText style={styles.drawerItemText}>
              Favoritos
            </ThemedText>
          </TouchableOpacity>

          {/* Botón de Programados */}
          <TouchableOpacity 
            style={styles.drawerItem} 
            onPress={() => {
              onClose();
              router.push('/scheduled');
            }}
          >
            <MaterialIcons 
              name="schedule" 
              size={24} 
              color={isDark ? '#ECEDEE' : '#11181C'} 
            />
            <ThemedText style={styles.drawerItemText}>
              Programados
            </ThemedText>
          </TouchableOpacity>

          {/* Botón de cerrar sesión */}
          <TouchableOpacity 
            style={styles.drawerItem} 
            onPress={handleLogout}
          >
            <MaterialIcons 
              name="logout" 
              size={24} 
              color={isDark ? '#ECEDEE' : '#11181C'} 
            />
            <ThemedText style={styles.drawerItemText}>
              Cerrar Sesión
            </ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </Animated.View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  overlay: {
    ...RNStyleSheet.absoluteFillObject,
    zIndex: 1000,
  },
  backdrop: {
    ...RNStyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  drawer: {
    position: "absolute",
    right: 0,
    top: 0,
    bottom: 0,
    width: DRAWER_WIDTH,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: -2, height: 0 },
    shadowOpacity: 0.25,
    shadowRadius: 5,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    marginBottom: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
  },
  closeButton: {
    padding: 10,
  },
  closeButtonText: {
    fontSize: 18,
  },
  userInfo: {
    alignItems: "center",
    marginBottom: 20,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 20,
  },
  name: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 4,
  },
  email: {
    fontSize: 16,
    color: "#666",
    marginBottom: 20,
  },
  settingsSection: {
    width: "100%",
    marginBottom: 20,
  },
  settingRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(150, 150, 150, 0.2)",
  },
  menuSection: {
    width: '100%',
    paddingHorizontal: 16,
  },
  drawerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  drawerItemText: {
    marginLeft: 16,
    fontSize: 16,
  },
  bottomButton: {
    marginBottom: 20,
  },
  logoutButton: {
    backgroundColor: '#ff4444',
    padding: 12,
    borderRadius: 8,
    marginHorizontal: 16,
    alignItems: 'center',
  },
  logoutText: {
    color: '#ffffff',
    fontWeight: '600',
  },
});

export default ProfileDrawer;
