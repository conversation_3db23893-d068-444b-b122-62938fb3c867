import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  Dimensions,
} from 'react-native';

interface CustomPickerProps {
  selectedValue: any;
  onValueChange: (value: any) => void;
  items: Array<{ label: string; value: any }>;
  isDark?: boolean;
  label?: string;
}

interface CustomPickerModalProps extends CustomPickerProps {
  visible: boolean;
  onClose: () => void;
}

const CustomPickerModal: React.FC<CustomPickerModalProps> = ({
  visible,
  onClose,
  selectedValue,
  onValueChange,
  items,
  isDark,
}) => {
  const handleSelect = (value: any) => {
    onValueChange(value);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[
          styles.modalContent,
          isDark && styles.modalContentDark
        ]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={onClose}>
              <Text style={[
                styles.modalHeaderText,
                isDark && styles.modalHeaderTextDark
              ]}>Cancelar</Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={items}
            style={styles.list}
            keyExtractor={(item) => item.value.toString()}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[
                  styles.item,
                  selectedValue === item.value && styles.selectedItem,
                  isDark && styles.itemDark,
                  selectedValue === item.value && isDark && styles.selectedItemDark,
                ]}
                onPress={() => handleSelect(item.value)}
              >
                <Text style={[
                  styles.itemText,
                  isDark && styles.itemTextDark,
                  selectedValue === item.value && styles.selectedItemText
                ]}>
                  {item.label}
                </Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </View>
    </Modal>
  );
};

const CustomPicker: React.FC<CustomPickerProps> = ({
  selectedValue,
  onValueChange,
  items,
  isDark,
  label,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const selectedItem = items.find((item) => item.value === selectedValue);

  return (
    <View style={styles.container}>
      {label && (
        <Text style={[styles.label, isDark && styles.labelDark]}>{label}</Text>
      )}
      <TouchableOpacity
        style={[styles.pickerButton, isDark && styles.pickerButtonDark]}
        onPress={() => setModalVisible(true)}
      >
        <Text style={[
          styles.pickerButtonText,
          isDark && styles.pickerButtonTextDark
        ]}>
          {selectedItem ? selectedItem.label : "Seleccionar"}
        </Text>
      </TouchableOpacity>

      <CustomPickerModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onValueChange={onValueChange}
        selectedValue={selectedValue}
        items={items}
        isDark={isDark}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#000',
  },
  labelDark: {
    color: '#fff',
  },
  pickerButton: {
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  pickerButtonDark: {
    backgroundColor: '#333',
    borderColor: '#444',
  },
  pickerButtonText: {
    fontSize: 16,
    color: '#000',
  },
  pickerButtonTextDark: {
    color: '#fff',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: Dimensions.get('window').height * 0.7,
  },
  modalContentDark: {
    backgroundColor: '#1c1c1e',
  },
  modalHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  modalHeaderText: {
    color: '#007AFF',
    fontSize: 16,
  },
  modalHeaderTextDark: {
    color: '#0A84FF',
  },
  list: {
    maxHeight: Dimensions.get('window').height * 0.6,
  },
  item: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  itemDark: {
    borderBottomColor: '#333',
  },
  selectedItem: {
    backgroundColor: '#f0f0f0',
  },
  selectedItemDark: {
    backgroundColor: '#2c2c2e',
  },
  itemText: {
    fontSize: 16,
    color: '#000',
  },
  itemTextDark: {
    color: '#fff',
  },
  selectedItemText: {
    fontWeight: '600',
  },
});

export default CustomPicker;
