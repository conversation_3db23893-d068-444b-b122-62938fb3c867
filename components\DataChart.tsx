// DataChart.tsx
import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Dimensions,
  ScrollView,
  TouchableOpacity,
  Modal,
  Pressable,
} from "react-native";
import { useColorScheme } from "@/hooks/useColorScheme";
import { LineChart } from "react-native-chart-kit";
import HeatMapGraph from "./HeatMapGraph";
import GroupedBarChart from "@/components/GroupedBarChart";
import HeatMapChart, { generateHeatMapData } from "@/components/HeatMapChart";

// Importamos los estilos desde el archivo separado (DataChart.styles.ts)
import styles from "./DataChart.styles";

interface PickerItem {
  label: string;
  value: string;
}

interface CustomPickerModalProps {
  visible: boolean;
  onClose: () => void;
  onValueChange: (value: string) => void;
  selectedValue: string;
  items: PickerItem[];
  isDark: boolean;
}

interface CustomPickerProps {
  selectedValue: string;
  onValueChange: (value: string) => void;
  items: PickerItem[];
  isDark: boolean;
}

interface DataChartProps {
  data: any[];
  chartType?: "line" | "bar" | "heat";
  onChartTypeChange?: (type: "line" | "bar" | "heat") => void;
}

const CustomPickerModal: React.FC<CustomPickerModalProps> = ({
  visible,
  onClose,
  onValueChange,
  selectedValue,
  items,
  isDark,
}) => (
  <Modal
    visible={visible}
    transparent={true}
    animationType="slide"
    onRequestClose={onClose}
  >
    <Pressable style={styles.modalOverlay} onPress={onClose}>
      <View style={[styles.modalContent, isDark && styles.modalContentDark]}>
        <View style={styles.modalHeader}>
          <Text
            style={[
              styles.modalHeaderText,
              isDark && styles.modalHeaderTextDark,
            ]}
          >
            Seleccionar opción
          </Text>
          <TouchableOpacity onPress={onClose}>
            <Text
              style={[
                styles.modalCloseText,
                isDark && styles.modalCloseTextDark,
              ]}
            >
              Cerrar
            </Text>
          </TouchableOpacity>
        </View>
        <ScrollView style={styles.modalScrollView}>
          {items.map((item) => (
            <TouchableOpacity
              key={item.value}
              style={[
                styles.modalItem,
                selectedValue === item.value && styles.modalItemSelected,
                isDark && styles.modalItemDark,
                selectedValue === item.value &&
                  isDark &&
                  styles.modalItemSelectedDark,
              ]}
              onPress={() => {
                onValueChange(item.value);
                onClose();
              }}
            >
              <Text
                style={[
                  styles.modalItemText,
                  selectedValue === item.value && styles.modalItemTextSelected,
                  isDark && styles.modalItemTextDark,
                ]}
              >
                {item.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    </Pressable>
  </Modal>
);

const CustomPicker: React.FC<CustomPickerProps> = ({
  selectedValue,
  onValueChange,
  items,
  isDark,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const selectedItem = items.find((item) => item.value === selectedValue);

  return (
    <>
      <TouchableOpacity
        style={[styles.pickerButton, isDark && styles.pickerButtonDark]}
        onPress={() => setModalVisible(true)}
      >
        <Text
          style={[
            styles.pickerButtonText,
            isDark && styles.pickerButtonTextDark,
          ]}
        >
          {selectedItem ? selectedItem.label : "Seleccionar"}
        </Text>
      </TouchableOpacity>

      <CustomPickerModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onValueChange={onValueChange}
        selectedValue={selectedValue}
        items={items}
        isDark={isDark}
      />
    </>
  );
};

export const DataChart: React.FC<DataChartProps> = ({
  data,
  chartType = "bar",
  onChartTypeChange,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === "dark";
  const [chartData, setChartData] = useState<any>(null);
  const screenWidth = Dimensions.get("window").width - 32;

  // Estados para filtros de fecha
  const [dateColumns, setDateColumns] = useState<string[]>([]);
  const [selectedDateColumn, setSelectedDateColumn] = useState<string>("");
  const [availableYears, setAvailableYears] = useState<number[]>([]);
  const [availableMonths, setAvailableMonths] = useState<number[]>([]);
  const [selectedYear, setSelectedYear] = useState<string>("all");
  const [selectedMonth, setSelectedMonth] = useState<string>("all");
  const [filteredData, setFilteredData] = useState<any[]>([]);

  // Estados para agrupación y métricas
  const [categoricalColumns, setCategoricalColumns] = useState<string[]>([]);
  const [selectedGroupBy, setSelectedGroupBy] = useState<string>("");
  const [numericColumns, setNumericColumns] = useState<string[]>([]);
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([]);
  const [potentialGroupingFields, setPotentialGroupingFields] = useState<
    string[]
  >([]);

  // Estados para la configuración del heatmap
  const [selectedHorizontalGroup, setSelectedHorizontalGroup] = useState("");
  const [selectedVerticalGroup, setSelectedVerticalGroup] = useState("");

  // Estado para el tipo de gráfica
  const [currentChartType, setCurrentChartType] = useState<
    "line" | "bar" | "heat"
  >(chartType);

  // Estado para controlar si hay datos suficientes para el heatmap
  const [heatMapAvailable, setHeatMapAvailable] = useState<boolean>(false);

  // Nuevos estados para la comparación
  const [showComparison, setShowComparison] = useState(false);
  const [comparisonChartType, setComparisonChartType] = useState<'line' | 'bar'>('line');
  const [comparisonSelectedYear, setComparisonSelectedYear] = useState<string>(() => {
    if (availableYears.length > 1) {
      const firstAvailableYear = availableYears[0].toString();
      return firstAvailableYear !== selectedYear ? firstAvailableYear : availableYears[1].toString();
    }
    return "all";
  });
  const [comparisonSelectedMonth, setComparisonSelectedMonth] = useState<string>('all');

  // Función para obtener el año de comparación por defecto
  const getDefaultComparisonYear = (currentYear: string): string => {
    if (!availableYears.length) return "all";
    
    // Si el año actual es "all", tomar el año más reciente
    if (currentYear === "all") {
      return availableYears[0].toString();
    }
    
    // Buscar el año anterior al seleccionado
    const currentYearIndex = availableYears.findIndex(
      year => year.toString() === currentYear
    );
    
    if (currentYearIndex >= 0 && currentYearIndex < availableYears.length - 1) {
      return availableYears[currentYearIndex + 1].toString();
    }
    
    // Si no hay año anterior, tomar el siguiente
    return availableYears[0].toString();
  };

  // Modificar el useEffect que maneja la activación de la comparación
  useEffect(() => {
    if (showComparison) {
      // Obtener todos los años disponibles excepto el seleccionado en el filtro principal
      const availableComparisonYears = availableYears
        .filter(year => year.toString() !== selectedYear)
        .map(year => year.toString());

      // Si no hay un año de comparación seleccionado o el actual no es válido
      if (comparisonSelectedYear === "all" || 
          comparisonSelectedYear === selectedYear || 
          !availableComparisonYears.includes(comparisonSelectedYear)) {
        // Seleccionar el primer año disponible
        if (availableComparisonYears.length > 0) {
          setComparisonSelectedYear(availableComparisonYears[0]);
          // Reset del mes al cambiar el año
          setComparisonSelectedMonth("all");
        }
      }
    }
  }, [showComparison, selectedYear, availableYears]);

  // Asignamos valores por defecto para los agrupadores desde que se detectan los campos categóricos
  useEffect(() => {
    if (categoricalColumns.length > 0) {
      if (!selectedHorizontalGroup) {
        setSelectedHorizontalGroup(categoricalColumns[0]);
      }
      if (categoricalColumns.length > 1 && !selectedVerticalGroup) {
        setSelectedVerticalGroup(categoricalColumns[1]);
      }
    }
  }, [categoricalColumns]);

  // Actualizamos la disponibilidad del heatmap cuando alguno de los estados relevantes cambie
  useEffect(() => {
    const condition =
      Array.isArray(filteredData) &&
      filteredData.length > 0 &&
      selectedHorizontalGroup !== "" &&
      selectedVerticalGroup !== "" &&
      selectedHorizontalGroup !== selectedVerticalGroup && // Asegurar que sean diferentes
      selectedMetrics.length > 0 &&
      categoricalColumns.length > 1; // Asegurar que haya al menos 2 campos categóricos
    setHeatMapAvailable(condition);
  }, [
    filteredData,
    selectedHorizontalGroup,
    selectedVerticalGroup,
    selectedMetrics,
    categoricalColumns,
  ]);

  // Función para generar datos demo para ContributionGraph (si se requiere)
  const generateContributionData = () => {
    const today = new Date();
    const commitsData = [];
    for (let i = 0; i < 105; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      const shouldHaveValue = Math.random() > 0.3;
      const value = shouldHaveValue ? Math.floor(Math.random() * 20) : 0;
      commitsData.push({
        date: date.toISOString().split("T")[0],
        count: value,
      });
    }
    return commitsData;
  };

  useEffect(() => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      setChartData(null);
      return;
    }
    analyzeDataStructure();
  }, [data]);

  useEffect(() => {
    applyFilters();
  }, [data, selectedYear, selectedMonth, selectedDateColumn]);

  useEffect(() => {
    if (
      filteredData.length > 0 &&
      selectedGroupBy &&
      selectedMetrics.length > 0
    ) {
      processChartData();
    }
  }, [
    filteredData,
    currentChartType,
    colorScheme,
    selectedGroupBy,
    selectedMetrics,
  ]);

  // Función para analizar la estructura de los datos
  const analyzeDataStructure = () => {
    try {
      if (!data || data.length === 0) return;
      const firstItem = data[0];
      const dateColumnsList = findDateColumns(firstItem);
      setDateColumns(dateColumnsList.sort((a, b) => a.localeCompare(b)));
      if (dateColumnsList.length > 0) {
        setSelectedDateColumn(dateColumnsList[0]);
      }
      
      // Excluir campos con títulos vacíos
      const excludedFields = ["ID", "MovID", "Consecutivo", "Sucursal", ""];
      
      // Campos que no deben usarse como métricas
      const nonMetricFields = [
        ...excludedFields,
        // Campos de agrupación que no deben ser métricas
        'Ejercicio', 'ejercicio', 'Periodo', 'periodo', 'Período', 'período',
        // Campos de doble propósito (ya incluidos en dualPurposeFields)
        "Año", "Anio", "Ano", "Year", "Mes", "Month"
      ];
      
      // Campos que pueden ser tanto agrupadores como métricas (ahora solo agrupadores)
      const dualPurposeFields = ["Año", "Anio", "Ano", "Year", "Mes", "Month"];
      
      const commonGroupingFields = [
        "Dia",
        "Semana",
        "Trimestre",
        "Ejercicio",
        "Periodo",
      ].sort((a, b) => a.localeCompare(b));

      // Extraer año y mes de las columnas de fecha
      let extractedDateFields = new Set<string>();
      if (dateColumnsList.length > 0) {
        data.forEach(item => {
          dateColumnsList.forEach(dateColumn => {
            const dateValue = item[dateColumn];
            if (dateValue) {
              let date: Date;
              if (dateValue instanceof Date) {
                date = dateValue;
              } else if (typeof dateValue === "string") {
                date = new Date(dateValue);
              } else return;

              if (!isNaN(date.getTime())) {
                const year = date.getFullYear().toString();
                const month = new Date(date.getFullYear(), date.getMonth(), 1)
                  .toLocaleString("es", { month: "long" });
                
                // Agregar campos derivados al item
                item[`${dateColumn}_Año`] = year;
                item[`${dateColumn}_Mes`] = month;
                
                // Agregar los nombres de los campos a nuestro conjunto
                extractedDateFields.add(`${dateColumn}_Año`);
                extractedDateFields.add(`${dateColumn}_Mes`);
              }
            }
          });
        });
      }

      const detectedGroupingFields = Object.keys(firstItem)
        .filter((key) =>
          commonGroupingFields.some((field) =>
            key.toLowerCase().includes(field.toLowerCase())
          )
        )
        .sort((a, b) => a.localeCompare(b));

      setPotentialGroupingFields(
        [...detectedGroupingFields, ...dualPurposeFields].sort((a, b) => 
          a.localeCompare(b)
        )
      );

      // Primero identificamos los campos de doble propósito presentes en los datos
      const presentDualPurposeFields = Object.keys(firstItem)
        .filter((key) =>
          dualPurposeFields.some(field => 
            key.toLowerCase().includes(field.toLowerCase())
          )
        )
        .sort((a, b) => a.localeCompare(b));

      // Ordenamos las columnas numéricas alfabéticamente
      let numericColumnsList = Object.keys(firstItem)
        .filter(
          (key) =>
            // Verificar que el título no esté vacío
            key.trim() !== "" &&
            // Verificar si es numérico
            (typeof firstItem[key] === "number" ||
              (!isNaN(Number(firstItem[key])) &&
                firstItem[key] !== null &&
                firstItem[key] !== "")) &&
            // Excluir campos que no deben ser métricas
            !nonMetricFields.some(field => 
              key.toLowerCase().includes(field.toLowerCase())
            ) &&
            !extractedDateFields.has(key) &&
            !detectedGroupingFields.includes(key)
        )
        .sort((a, b) => a.localeCompare(b));

      setNumericColumns(numericColumnsList);

      // Simplemente seleccionamos la primera métrica disponible
      if (numericColumnsList.length > 0) {
        setSelectedMetrics([numericColumnsList[0]]);
      } else {
        setSelectedMetrics([]);
      }

      // Modificamos la lógica para las columnas categóricas
      let categoricalColumnsList = Object.keys(firstItem)
        .filter(
          (key) =>
            // Verificar que el título no esté vacío
            key.trim() !== "" &&
            ((!numericColumnsList.includes(key) &&
            !dateColumnsList.includes(key) &&
            typeof firstItem[key] === "string") ||
            detectedGroupingFields.includes(key) ||
            extractedDateFields.has(key))
        )
        .sort((a, b) => a.localeCompare(b));

      // Agregamos explícitamente los campos de doble propósito y ordenamos alfabéticamente
      categoricalColumnsList = [...new Set([
        ...categoricalColumnsList,
        ...presentDualPurposeFields,
        ...Array.from(extractedDateFields) // Agregamos los campos derivados de fecha
      ])].sort((a, b) => a.localeCompare(b));

      setCategoricalColumns(categoricalColumnsList);
      if (categoricalColumnsList.length > 0) {
        setSelectedGroupBy(categoricalColumnsList[0]);
      }
      setFilteredData(data);
      if (dateColumnsList.length > 0) {
        extractYearsAndMonths(dateColumnsList[0]);
      }
    } catch (error) {
      console.error("Error analyzing data structure:", error);
    }
  };

  const findDateColumns = (item: any) => {
    const dateKeywords = [
      "fecha",
      "date",
      "time",
      "día",
      "año",
      "mes",
      "year",
      "month",
    ];
    return Object.keys(item).filter((key) => {
      const keyLowerCase = key.toLowerCase();
      const keyContainsDateWord = dateKeywords.some((keyword) =>
        keyLowerCase.includes(keyword)
      );
      const value: unknown = item[key];
      const isDateString =
        (typeof value === "string" &&
          (/^\d{4}-\d{2}-\d{2}(T|\s|$)/.test(value) ||
            /^\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}$/.test(value))) ||
        Object.prototype.toString.call(value) === "[object Date]";
      return keyContainsDateWord || isDateString;
    });
  };

  const extractYearsAndMonths = (dateColumn: string) => {
    if (!data || data.length === 0) return;
    try {
      const years = new Set<number>();
      // Cambiamos a un Map para mantener la relación año -> meses
      const monthsByYear = new Map<number, Set<number>>();
      
      data.forEach((item) => {
        const dateValue = item[dateColumn];
        if (!dateValue) return;
        let date: Date;
        if (dateValue instanceof Date) {
          date = dateValue;
        } else if (typeof dateValue === "string") {
          date = new Date(dateValue);
        } else return;

        if (!isNaN(date.getTime())) {
          const year = date.getFullYear();
          const month = date.getMonth() + 1;
          
          years.add(year);
          
          // Agregamos el mes al conjunto de meses del año correspondiente
          if (!monthsByYear.has(year)) {
            monthsByYear.set(year, new Set<number>());
          }
          monthsByYear.get(year)?.add(month);
        }
      });

      const sortedYears = Array.from(years).sort((a, b) => b - a);
      setAvailableYears(sortedYears);
      
      // Guardamos la referencia a monthsByYear para usarla cuando cambie el año
      setMonthsByYear(monthsByYear);
      
      // Establecemos el año más reciente como valor inicial
      if (sortedYears.length > 0) {
        const mostRecentYear = sortedYears[0].toString();
        setSelectedYear(mostRecentYear);
        
        // Actualizamos los meses disponibles para el año seleccionado
        const monthsForYear = Array.from(monthsByYear.get(sortedYears[0]) || []).sort((a, b) => a - b);
        setAvailableMonths(monthsForYear);
      }
    } catch (error) {
      console.error("Error extracting years and months:", error);
    }
  };

  // Agregamos un nuevo estado para mantener la relación año -> meses
  const [monthsByYear, setMonthsByYear] = useState<Map<number, Set<number>>>(new Map());

  // Agregamos un nuevo estado para los meses disponibles en la comparación
  const [comparisonAvailableMonths, setComparisonAvailableMonths] = useState<number[]>([]);

  // Agregamos un useEffect para actualizar los meses disponibles cuando cambie el año
  useEffect(() => {
    if (selectedYear !== "all" && monthsByYear.size > 0) {
      const monthsForYear = Array.from(monthsByYear.get(parseInt(selectedYear)) || [])
        .sort((a, b) => a - b);
      setAvailableMonths(monthsForYear);
      
      if (selectedMonth !== "all" && !monthsForYear.includes(parseInt(selectedMonth))) {
        setSelectedMonth("all");
      }
    } else {
      const allMonths = new Set<number>();
      monthsByYear.forEach(months => {
        months.forEach(month => allMonths.add(month));
      });
      setAvailableMonths(Array.from(allMonths).sort((a, b) => a - b));
    }

    // Actualizar meses disponibles para la comparación
    if (comparisonSelectedYear !== "all" && monthsByYear.size > 0) {
      // Obtenemos los meses del año de comparación
      const comparisonYearMonths = new Set(
        Array.from(monthsByYear.get(parseInt(comparisonSelectedYear)) || [])
      );

      // Obtenemos los meses del año principal si está seleccionado
      const primaryYearMonths = selectedYear !== "all" 
        ? new Set(Array.from(monthsByYear.get(parseInt(selectedYear)) || []))
        : new Set<number>();

      // Combinamos ambos conjuntos de meses
      const combinedMonths = new Set([...comparisonYearMonths, ...primaryYearMonths]);
      
      // Convertimos a array y ordenamos
      const sortedCombinedMonths = Array.from(combinedMonths).sort((a, b) => a - b);
      setComparisonAvailableMonths(sortedCombinedMonths);
      
      if (comparisonSelectedMonth !== "all" && !sortedCombinedMonths.includes(parseInt(comparisonSelectedMonth))) {
        setComparisonSelectedMonth("all");
      }
    }
  }, [selectedYear, comparisonSelectedYear, monthsByYear]);

  const applyFilters = () => {
    if (!data || data.length === 0) return;
    try {
      let filtered = [...data];
      if (
        selectedDateColumn &&
        (selectedYear !== "all" || selectedMonth !== "all")
      ) {
        filtered = filtered.filter((item) => {
          const dateValue = item[selectedDateColumn];
          if (!dateValue) return false;
          let date: Date;
          if (dateValue instanceof Date) {
            date = dateValue;
          } else if (typeof dateValue === "string") {
            date = new Date(dateValue);
          } else return false;
          if (isNaN(date.getTime())) return false;
          if (
            selectedYear !== "all" &&
            date.getFullYear() !== parseInt(selectedYear)
          )
            return false;
          if (
            selectedMonth !== "all" &&
            date.getMonth() + 1 !== parseInt(selectedMonth)
          )
            return false;
          return true;
        });
      }
      setFilteredData(filtered);
    } catch (error) {
      console.error("Error applying filters:", error);
      setFilteredData(data);
    }
  };

  const getRandomColor = (index: number) => {
    const colors = [
      "#4285F4",
      "#EA4335",
      "#FBBC05",
      "#34A853",
      "#8AB4F8",
      "#F28B82",
      "#FDD663",
      "#81C995",
      "#5E97F6",
      "#E67C73",
      "#F2BF45",
      "#57BB8A",
    ];
    return colors[index % colors.length];
  };

  const toggleMetric = (metric: string) => {
    if (currentChartType === "heat") {
      // Para el mapa de calor, solo permitimos una métrica
      setSelectedMetrics([metric]);
    } else {
      // Para otros tipos de gráficas, permitimos múltiples métricas
      if (selectedMetrics.includes(metric)) {
        setSelectedMetrics((prev) => prev.filter((m) => m !== metric));
      } else {
        setSelectedMetrics((prev) => [...prev, metric]);
      }
    }
  };

  // Procesa data para las gráficas (línea y barras)
  const processChartData = () => {
    try {
      if (!filteredData || filteredData.length === 0 || !selectedGroupBy || selectedMetrics.length === 0) {
        setChartData({
          bar: {
            labels: [],
            datasets: [],
            legend: []
          },
          line: {
            labels: [],
            datasets: [],
            legend: []
          },
          heat: null
        });
        return;
      }

      // Definir el tipo para los datos agrupados
      type MetricData = {
        total: number;
        count: number;
      };

      type GroupedDataMap = Map<string, Record<string, MetricData>>;

      // Función auxiliar para procesar un conjunto de datos
      const processDataSet = (dataSet: any[], yearLabel: string): GroupedDataMap => {
        const groupedData: GroupedDataMap = new Map();
        
        dataSet.forEach((item) => {
          const groupKey = item[selectedGroupBy] !== null && item[selectedGroupBy] !== undefined
            ? item[selectedGroupBy]?.toString()
            : "Sin valor";

          if (!groupedData.has(groupKey)) {
            const metricData: Record<string, MetricData> = {};
            selectedMetrics.forEach((metric) => {
              metricData[metric] = { total: 0, count: 0 };
            });
            groupedData.set(groupKey, metricData);
          }

          selectedMetrics.forEach((metric) => {
            const value = Number(item[metric] || 0);
            const current = groupedData.get(groupKey)![metric];
            groupedData.get(groupKey)![metric] = {
              total: current.total + (isNaN(value) ? 0 : value),
              count: current.count + 1,
            };
          });
        });

        return groupedData;
      };

      // Procesar datos principales
      const mainGroupedData = processDataSet(filteredData, selectedYear);

      // Procesar datos de comparación si está activada
      let comparisonGroupedData: GroupedDataMap | undefined;
      if (showComparison) {
        const comparisonData = data.filter(item => {
          const dateValue = new Date(item[selectedDateColumn]);
          return dateValue.getFullYear().toString() === comparisonSelectedYear &&
                 (comparisonSelectedMonth === "all" || 
                  (dateValue.getMonth() + 1).toString() === comparisonSelectedMonth);
        });
        comparisonGroupedData = processDataSet(comparisonData, comparisonSelectedYear);
      }

      // Obtener todas las etiquetas únicas
      const allLabels = new Set([
        ...Array.from(mainGroupedData.keys()),
        ...(comparisonGroupedData ? Array.from(comparisonGroupedData.keys()) : [])
      ]);

      const sortedLabels = Array.from(allLabels).sort((a, b) => {
        if (!isNaN(Number(a)) && !isNaN(Number(b))) {
          return Number(a) - Number(b);
        }
        return a.localeCompare(b);
      });

      // Crear datasets para cada métrica
      const datasets = selectedMetrics.map((metric, index) => ({
        data: sortedLabels.map(label => mainGroupedData.get(label)?.[metric]?.total || 0),
        color: () => getRandomColor(index),
        strokeWidth: 2,
        label: metric
      }));

      // Si hay comparación, añadir datasets adicionales
      if (showComparison && comparisonGroupedData) {
        selectedMetrics.forEach((metric, index) => {
          datasets.push({
            data: sortedLabels.map(label => comparisonGroupedData?.get(label)?.[metric]?.total || 0),
            color: () => getRandomColor(selectedMetrics.length + index),
            strokeWidth: 2,
            label: `${metric} (${comparisonSelectedYear})`
          });
        });
      }

      const lineData = {
        labels: sortedLabels,
        datasets,
        legend: datasets.map(d => d.label)
      };

      const barData = {
        labels: sortedLabels,
        datasets,
        legend: datasets.map(d => d.label)
      };

      setChartData({
        bar: barData,
        line: lineData,
        heat: null
      });

    } catch (error) {
      console.error("Error processing chart data:", error);
      setChartData({
        bar: { labels: [], datasets: [], legend: [] },
        line: { labels: [], datasets: [], legend: [] },
        heat: null
      });
    }
  };

  // Asegurarse de que processChartData se ejecute cuando cambian los años de comparación
  useEffect(() => {
    processChartData();
  }, [
    filteredData,
    selectedGroupBy,
    selectedMetrics,
    showComparison,
    comparisonSelectedYear,
    comparisonSelectedMonth
  ]);

  // Procesa la data para el heatmap usando los agrupadores seleccionados (o sus valores por defecto)
  const processHeatMapData = () => {
    if (
      !filteredData ||
      filteredData.length === 0 ||
      selectedMetrics.length === 0 ||
      selectedHorizontalGroup === "" ||
      selectedVerticalGroup === ""
    ) {
      setChartData((prev: any) => ({ ...prev, heat: null }));
      return;
    }
    const metric = selectedMetrics[0];
    const horizontalSet = new Set<string>();
    const verticalSet = new Set<string>();
    filteredData.forEach((item) => {
      const xVal = item[selectedHorizontalGroup] || "Sin valor";
      const yVal = item[selectedVerticalGroup] || "Sin valor";
      horizontalSet.add(xVal);
      verticalSet.add(yVal);
    });
    const horizontalLabels = Array.from(horizontalSet).sort();
    const verticalLabels = Array.from(verticalSet).sort();
    const matrix = verticalLabels.map(() => horizontalLabels.map(() => 0));
    filteredData.forEach((item) => {
      const xVal = item[selectedHorizontalGroup] || "Sin valor";
      const yVal = item[selectedVerticalGroup] || "Sin valor";
      const colIndex = horizontalLabels.indexOf(xVal);
      const rowIndex = verticalLabels.indexOf(yVal);
      const value = Number(item[metric] || 0);
      matrix[rowIndex][colIndex] += value;
    });
    setChartData((prev: any) => ({
      ...prev,
      heat: {
        horizontalLabels,
        verticalLabels,
        data: matrix,
        title: `${metric} agrupado por ${selectedHorizontalGroup} y ${selectedVerticalGroup}`,
      },
    }));
  };

  // Llama a processHeatMapData cuando se seleccione la gráfica de Calor
  useEffect(() => {
    if (currentChartType === "heat") {
      processHeatMapData();
    }
  }, [
    filteredData,
    currentChartType,
    selectedMetrics,
    selectedHorizontalGroup,
    selectedVerticalGroup,
    colorScheme,
  ]);

  const resetFilters = () => {
    setSelectedYear("all");
    setSelectedMonth("all");
    setFilteredData(data);
  };

  const handleChartTypeChange = (type: "line" | "bar" | "heat") => {
    setCurrentChartType(type);
    if (onChartTypeChange) {
      onChartTypeChange(type);
    }
  };

  if (!data || data.length === 0) {
    return (
      <View style={[styles.container, styles.emptyContainer]}>
        <Text style={[styles.text, isDark && styles.textDark]}>
          No hay datos disponibles
        </Text>
      </View>
    );
  }

  if (!chartData) {
    return (
      <View style={[styles.container, styles.emptyContainer]}>
        <Text style={[styles.text, isDark && styles.textDark]}>
          Procesando datos para gráfica...
        </Text>
      </View>
    );
  }

  if (!chartData || (!chartData.bar && !chartData.line && !chartData.heat)) {
    return (
      <View style={[styles.container, styles.emptyContainer]}>
        <Text style={[styles.text, isDark && styles.textDark]}>
          Procesando datos para gráfica...
        </Text>
      </View>
    );
  }

  const chartConfig = {
    backgroundColor: isDark ? "#1e1e1e" : "#ffffff",
    backgroundGradientFrom: isDark ? "#2a2a2a" : "#ffffff",
    backgroundGradientTo: isDark ? "#1a1a1a" : "#f5f5f5",
    decimalPlaces: 0,
    color: (opacity = 1) =>
      isDark ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
    labelColor: (opacity = 1) =>
      isDark ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
    style: {
      borderRadius: 16,
      paddingVertical: 8,
      paddingLeft: 20,
    },
    propsForDots: {
      r: "6",
      strokeWidth: "2",
      stroke: isDark ? "#fff" : "#333",
    },
    barPercentage: 1,
    useShadowColorFromDataset: false,
  };

  const dataCount = filteredData.length;

  return (
    <View style={styles.mainContainer}>
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContentContainer}
        showsVerticalScrollIndicator={true}
        bounces={true}
        alwaysBounceVertical={true}
        nestedScrollEnabled={true}
      >
        <View style={styles.contentContainer}>
          <Text style={[styles.chartTitle, isDark && styles.chartTitleDark]}>
            {currentChartType === "bar"
              ? "Gráfica de Barras"
              : currentChartType === "line"
              ? "Gráfica de Línea"
              : "Mapa de Calor"}
          </Text>

          {dateColumns.length > 0 && (
            <View style={styles.filterSection}>
              <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
                Filtros de Fecha
              </Text>
              {dateColumns.length > 1 && (
                <View style={styles.filterItem}>
                  <Text style={[styles.filterLabel, isDark && styles.textDark]}>
                    Campo de fecha:
                  </Text>
                  <CustomPicker
                    selectedValue={selectedDateColumn}
                    onValueChange={(value) => setSelectedDateColumn(value)}
                    items={dateColumns.map((column) => ({
                      label: column,
                      value: column,
                    }))}
                    isDark={isDark}
                  />
                </View>
              )}
              <View style={styles.filterItem}>
                <Text style={[styles.filterLabel, isDark && styles.textDark]}>
                  Año:
                </Text>
                <CustomPicker
                  selectedValue={selectedYear}
                  onValueChange={(value) => setSelectedYear(value)}
                  items={availableYears.map((year) => ({
                    label: year.toString(),
                    value: year.toString(),
                  }))}
                  isDark={isDark}
                />
              </View>
              <View style={styles.filterItem}>
                <Text style={[styles.filterLabel, isDark && styles.textDark]}>
                  Mes:
                </Text>
                <CustomPicker
                  selectedValue={selectedMonth}
                  onValueChange={(value) => setSelectedMonth(value)}
                  items={availableMonths.map((month) => ({
                    label: new Date(2000, month - 1, 1).toLocaleString("es", {
                      month: "long",
                    }),
                    value: month.toString(),
                  }))}
                  isDark={isDark}
                />
              </View>
              <TouchableOpacity
                style={[styles.resetButton, isDark && styles.resetButtonDark]}
                onPress={resetFilters}
              >
                <Text style={styles.resetButtonText}>Resetear filtros</Text>
              </TouchableOpacity>
            </View>
          )}

          {currentChartType === "heat" && (
            <View style={styles.filterSection}>
              <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
                Configuración de Agrupación para Heatmap
              </Text>
              <View style={styles.filterItem}>
                <Text style={[styles.filterLabel, isDark && styles.textDark]}>
                  Agrupador Horizontal:
                </Text>
                <CustomPicker
                  selectedValue={selectedHorizontalGroup}
                  onValueChange={(value) => {
                    setSelectedHorizontalGroup(value);
                    // Si el vertical es igual al que acabamos de seleccionar, cambiarlo
                    if (
                      value === selectedVerticalGroup &&
                      categoricalColumns.length > 1
                    ) {
                      const otherOption = categoricalColumns.find(
                        (col) => col !== value
                      );
                      if (otherOption) {
                        setSelectedVerticalGroup(otherOption);
                      }
                    }
                  }}
                  items={categoricalColumns.map((col) => ({
                    label: col,
                    value: col,
                  }))}
                  isDark={isDark}
                />
              </View>
              <View style={styles.filterItem}>
                <Text style={[styles.filterLabel, isDark && styles.textDark]}>
                  Agrupador Vertical:
                </Text>
                <CustomPicker
                  selectedValue={selectedVerticalGroup}
                  onValueChange={(value) => {
                    setSelectedVerticalGroup(value);
                    // Si el horizontal es igual al que acabamos de seleccionar, cambiarlo
                    if (
                      value === selectedHorizontalGroup &&
                      categoricalColumns.length > 1
                    ) {
                      const otherOption = categoricalColumns.find(
                        (col) => col !== value
                      );
                      if (otherOption) {
                        setSelectedHorizontalGroup(otherOption);
                      }
                    }
                  }}
                  items={categoricalColumns
                    .filter((col) =>
                      // Filtrar para que no aparezca el ya seleccionado en horizontal
                      categoricalColumns.length > 1
                        ? col !== selectedHorizontalGroup
                        : true
                    )
                    .map((col) => ({
                      label: col,
                      value: col,
                    }))}
                  isDark={isDark}
                />
              </View>
            </View>
          )}

          <View style={styles.filterSection}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              Configuración de Gráfica
            </Text>
            {currentChartType !== "heat" && (
              <View style={styles.filterItem}>
                <Text style={[styles.filterLabel, isDark && styles.textDark]}>
                  Agrupar por:
                </Text>
                <CustomPicker
                  selectedValue={selectedGroupBy}
                  onValueChange={(value) => setSelectedGroupBy(value)}
                  items={categoricalColumns.map((column) => ({
                    label: column,
                    value: column,
                  }))}
                  isDark={isDark}
                />
              </View>
            )}
            <View style={styles.filterItem}>
              <Text style={[styles.filterLabel, isDark && styles.textDark]}>
                Tipo:
              </Text>
              <View style={styles.chartTypeButtons}>
                <TouchableOpacity
                  style={[
                    styles.chartTypeButton,
                    currentChartType === "bar" && styles.chartTypeButtonActive,
                    isDark && styles.chartTypeButtonDark,
                  ]}
                  onPress={() => handleChartTypeChange("bar")}
                >
                  <Text
                    style={[
                      styles.chartTypeText,
                      currentChartType === "bar" && styles.chartTypeTextActive,
                      isDark && styles.chartTypeTextDark,
                    ]}
                  >
                    Barras
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.chartTypeButton,
                    currentChartType === "line" && styles.chartTypeButtonActive,
                    isDark && styles.chartTypeButtonDark,
                  ]}
                  onPress={() => handleChartTypeChange("line")}
                >
                  <Text
                    style={[
                      styles.chartTypeText,
                      currentChartType === "line" && styles.chartTypeTextActive,
                      isDark && styles.chartTypeTextDark,
                    ]}
                  >
                    Línea
                  </Text>
                </TouchableOpacity>
                {heatMapAvailable && (
                  <TouchableOpacity
                    style={[
                      styles.chartTypeButton,
                      currentChartType === "heat" &&
                        styles.chartTypeButtonActive,
                      isDark && styles.chartTypeButtonDark,
                    ]}
                    onPress={() => handleChartTypeChange("heat")}
                  >
                    <Text
                      style={[
                        styles.chartTypeText,
                        currentChartType === "heat" &&
                          styles.chartTypeTextActive,
                        isDark && styles.chartTypeTextDark,
                      ]}
                    >
                      Calor
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </View>

          {(currentChartType === 'bar' || currentChartType === 'line') && availableYears.length > 1 && (
            <View style={styles.filterSection}>
              <View style={styles.comparisonHeader}>
                <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
                  Comparación
                </Text>
                <TouchableOpacity
                  style={[
                    styles.comparisonToggle,
                    showComparison && styles.comparisonToggleActive,
                    isDark && styles.comparisonToggleDark
                  ]}
                  onPress={() => setShowComparison(!showComparison)}
                >
                  <Text style={[
                    styles.comparisonToggleText,
                    showComparison && styles.comparisonToggleTextActive
                  ]}>
                    {showComparison ? 'Desactivar' : 'Activar'} Comparación
                  </Text>
                </TouchableOpacity>
              </View>

              {showComparison && (
                <>
                  <View style={styles.filterItem}>
                    <Text style={[styles.filterLabel, isDark && styles.textDark]}>
                      Año de comparación:
                    </Text>
                    <CustomPicker
                      selectedValue={comparisonSelectedYear}
                      onValueChange={(value) => {
                        setComparisonSelectedYear(value);
                        // Reset del mes al cambiar el año
                        setComparisonSelectedMonth("all");
                      }}
                      items={[
                        ...availableYears
                          .filter(year => year.toString() !== selectedYear) // Excluir el año seleccionado en el filtro principal
                          .map((year) => ({
                            label: year.toString(),
                            value: year.toString(),
                          }))
                      ]}
                      isDark={isDark}
                    />
                  </View>
                  <View style={styles.filterItem}>
                    <Text style={[styles.filterLabel, isDark && styles.textDark]}>
                      Mes de comparación (opcional):
                    </Text>
                    <CustomPicker
                      selectedValue={comparisonSelectedMonth}
                      onValueChange={setComparisonSelectedMonth}
                      items={[
                        { label: "Todos los meses", value: "all" },
                        ...comparisonAvailableMonths.map((month) => ({
                          label: new Date(2000, month - 1, 1).toLocaleString("es", {
                            month: "long",
                          }),
                          value: month.toString(),
                        }))
                      ]}
                      isDark={isDark}
                    />
                  </View>
                  <View style={styles.filterItem}>
                    <Text style={[styles.filterLabel, isDark && styles.textDark]}>
                      Tipo de gráfica:
                    </Text>
                    <View style={styles.chartTypeButtons}>
                      <TouchableOpacity
                        style={[
                          styles.chartTypeButton,
                          comparisonChartType === 'bar' && styles.chartTypeButtonActive,
                          isDark && styles.chartTypeButtonDark,
                        ]}
                        onPress={() => setComparisonChartType('bar')}
                      >
                        <Text style={styles.chartTypeText}>Barras</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[
                          styles.chartTypeButton,
                          comparisonChartType === 'line' && styles.chartTypeButtonActive,
                          isDark && styles.chartTypeButtonDark,
                        ]}
                        onPress={() => setComparisonChartType('line')}
                      >
                        <Text style={styles.chartTypeText}>Línea</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </>
              )}
            </View>
          )}

          <View style={styles.filterSection}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              Métricas
            </Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {numericColumns.map((metric) => {
                const isActive = selectedMetrics.includes(metric);
                return (
                  <TouchableOpacity
                    key={metric}
                    style={[
                      styles.metricButton,
                      isActive && styles.metricButtonActive,
                    ]}
                    onPress={() => toggleMetric(metric)}
                  >
                    <Text
                      style={[
                        styles.metricButtonText,
                        isActive && styles.metricButtonTextActive,
                      ]}
                    >
                      {metric}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </View>

          <View style={styles.dataCountContainer}>
            <Text style={[styles.dataCount, isDark && styles.textDark]}>
              Mostrando {dataCount} {dataCount === 1 ? "registro" : "registros"}
            </Text>
          </View>

          <ScrollView
            horizontal
            style={styles.chartScrollContainer}
            contentContainerStyle={styles.chartContentContainer}
            showsHorizontalScrollIndicator={true}
          >
            {currentChartType === "bar" && chartData.bar ? (
              <GroupedBarChart
                data={{
                  labels: chartData.bar.labels || [],
                  datasets: chartData.bar.datasets || [],
                  legend: chartData.bar.legend || []
                }}
                width={Math.max(
                  screenWidth,
                  (chartData.bar.labels || []).length * 70
                )}
                height={400}
                chartConfig={chartConfig}
                horizontalLabelRotation={25}
                style={styles.chart}
                showValuesOnTopOfBars={true}
              />
            ) : currentChartType === "line" && chartData.line ? (
              <LineChart
                data={{
                  labels: chartData.line.labels || [],
                  datasets: chartData.line.datasets || [],
                  legend: chartData.line.legend || []
                }}
                width={Math.max(screenWidth, (chartData.line.labels || []).length * 70)}
                height={320}
                chartConfig={chartConfig}
                bezier
                style={styles.chart}
                yAxisLabel=""
                yAxisSuffix=""
                yAxisInterval={1}
                horizontalLabelRotation={0}
                withInnerLines={true}
                segments={5}
                yLabelsOffset={-10}
              />
            ) : currentChartType === "heat" && chartData?.heat ? (
              <View style={styles.chartContainer}>
                <Text
                  style={[styles.chartTitle, isDark && styles.chartTitleDark]}
                >
                  {chartData.heat.title}
                </Text>
                <HeatMapGraph
                  verticalLabels={chartData.heat.verticalLabels ?? []}
                  horizontalLabels={chartData.heat.horizontalLabels ?? []}
                  data={chartData.heat.data ?? []}
                />
              </View>
            ) : null}
          </ScrollView>
        </View>
      </ScrollView>
    </View>
  );
};

export default DataChart;
