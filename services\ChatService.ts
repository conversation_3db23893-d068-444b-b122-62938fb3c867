import { ENDPOINTS } from '@/constants/Api';

// Define the message types based on the API response
export interface ChatMessage {
  ID: number;
  RID: number;
  Usuario: string;
  UsuarioDestino: string;
  Mensaje: string;
  FechaRegistro: string;
  MensajeTipo: string;
  Respuesta: any; // This can be a string or an array of objects for TablaJSON
}

// Map botId to category name (bot name)
// This should be populated from the bot list API response
const botCategoryMap: Record<number, string> = {};

/**
 * Gets the category name for a bot
 * @param botId The ID of the bot
 * @returns The category name or a default
 */
export const getBotCategoryName = (botId: number): string => {
  return botCategoryMap[botId] || `BotConsulta`; // Default to BotConsulta if unknown
};

/**
 * Sets the category name for a bot
 * @param botId The ID of the bot
 * @param categoryName The category name
 */
export const setBotCategoryName = (botId: number, categoryName: string): void => {
  botCategoryMap[botId] = categoryName;
};

/**
 * Fetches chat history for a specific bot
 * @param botId The ID of the bot to fetch history for
 * @param accessToken The user's access token
 * @returns Promise with the chat history
 */
export const fetchChatHistory = async (botId: number, accessToken: string): Promise<ChatMessage[]> => {
  try {
    // Create FormData object
    const formData = new FormData();
    formData.append('categoryId', botId.toString());

    console.log(`Fetching chat history for bot ${botId}`);
    
    const response = await fetch(ENDPOINTS.HISTORY, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
      },
      body: formData,
    });

    const responseText = await response.text();
    console.log('History response status:', response.status);
    
    if (!response.ok) {
      console.error(`Failed to fetch history: ${response.status}`, responseText);
      throw new Error(`Failed to fetch history: ${response.status}`);
    }

    // Check if response is empty
    if (!responseText || responseText.trim() === '') {
      console.log('Empty response from history API');
      return [];
    }

    try {
      const parsedData = JSON.parse(responseText);
      console.log('History response length:', Array.isArray(parsedData) ? parsedData.length : 'not an array');
      
      // Process the data to ensure all TablaJSON responses are properly parsed
      if (Array.isArray(parsedData)) {
        return parsedData.map(message => {
          // If the message has a TablaJSON type and the response is a string, try to parse it
          if (message.MensajeTipo === 'TablaJSON' && typeof message.Respuesta === 'string') {
            try {
              message.Respuesta = JSON.parse(message.Respuesta);
            } catch (e) {
              console.error('Failed to parse TablaJSON response:', e);
              // Keep it as a string if parsing fails
            }
          }
          return message;
        });
      }
      
      return Array.isArray(parsedData) ? parsedData : [];
    } catch (parseError) {
      console.error('Error parsing history response:', parseError);
      console.error('Response text:', responseText);
      return [];
    }
  } catch (error) {
    console.error('Error fetching chat history:', error);
    throw error;
  }
};

/**
 * Extracts the text content from a chat message response
 * @param message The chat message
 * @returns The text content
 */
export const extractMessageContent = (message: ChatMessage): string => {
  if (!message) return '';
  
  // If it's a text message, return the Mensaje field
  if (message.MensajeTipo === 'Texto') {
    if (message.Respuesta && typeof message.Respuesta === 'string') {
      return message.Respuesta;
    }
    return message.Mensaje || '';
  }
  
  // If it's a TablaJSON message, return a description
  if (message.MensajeTipo === 'TablaJSON') {
    if (message.Respuesta) {
      try {
        const data = typeof message.Respuesta === 'string' 
          ? JSON.parse(message.Respuesta) 
          : message.Respuesta;
          
        if (Array.isArray(data)) {
          return `Table with ${data.length} rows`;
        }
      } catch (e) {
        console.error('Error parsing TablaJSON:', e);
      }
    }
    return 'Table data';
  }
  
  // For other types, stringify the response if it's not a string
  if (message.Respuesta) {
    if (typeof message.Respuesta === 'string') {
      return message.Respuesta;
    }
    try {
      return JSON.stringify(message.Respuesta, null, 2);
    } catch (e) {
      return 'Complex data (cannot display)';
    }
  }
  return message.Mensaje || '';
};

/**
 * Parses the response data based on message type
 * @param message The chat message to parse
 * @returns The formatted response
 */
export const parseMessageResponse = (message: ChatMessage): any => {
  if (message.MensajeTipo === 'TablaJSON' && typeof message.Respuesta === 'string') {
    try {
      return JSON.parse(message.Respuesta);
    } catch (e) {
      console.error('Error parsing TablaJSON response:', e);
      return message.Respuesta;
    }
  }
  return message.Respuesta;
};

/**
 * Sends a message to a bot and returns the response
 * @param botId The ID of the bot to send the message to
 * @param message The message text to send
 * @param accessToken The user's access token
 * @param userId The user's ID
 * @returns Promise with the chat message response
 */
export const sendMessage = async (
  botId: number, 
  message: string, 
  accessToken: string,
  userId: string
): Promise<ChatMessage | null> => {
  try {
    // Get the bot category name
    const botCategory = getBotCategoryName(botId);
    
    // Create FormData object
    const formData = new FormData();
    formData.append('user_id', userId);
    formData.append('category_name', botCategory);
    formData.append('message', message);
    formData.append('provider', "Google");

    console.log(`Sending message to bot ${botId} (${botCategory}): ${message}`);
    
    const response = await fetch(ENDPOINTS.QUERIES, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
      },
      body: formData,
    });

    console.log('Query response status:', response.status);
    
    if (response.status === 401) {
      // Token expired
      console.error('Authorization token expired or invalid');
      throw new Error('Authorization token expired or invalid');
    } else if (response.status === 424) {
      // API error with details
      const result = await response.json();
      console.error('API error:', result.detail);
      
      // Create an error message that can be displayed to the user
      const errorMessage: ChatMessage = {
        ID: Date.now(),
        RID: Date.now(),
        Usuario: userId,
        UsuarioDestino: '',
        Mensaje: message,
        FechaRegistro: new Date().toISOString(),
        MensajeTipo: 'Texto',
        Respuesta: result.detail || 'Error en la consulta'
      };
      
      return errorMessage;
    } else if (!response.ok) {
      const responseText = await response.text();
      console.error(`Failed to send message: ${response.status}`, responseText);
      throw new Error(`Failed to send message: ${response.status}`);
    }

    // Get response as text first
    const responseText = await response.text();
    
    // Check if response is empty
    if (!responseText || responseText.trim() === '') {
      console.log('Empty response from queries API');
      return null;
    }

    try {
      const result = JSON.parse(responseText);
      console.log('Query response parsed:', JSON.stringify(result).substring(0, 200) + '...');
      
      // Determine if the response contains actual data
      const hasResponse = result.wdata || 
                          (result.answer && result.answer !== '' && result.answer !== 'Sin respuesta') || 
                          (result.status && result.status === 'success' && result.answer);
      
      if (!hasResponse) {
        console.log('No meaningful data in response');
        return null;
      }
      
      // Determine the response content and type
      let responseContent = null;
      let messageType = 'Texto';
      
      if (result.wdata) {
        // If we have wdata, it's a table response
        responseContent = result.wdata;
        messageType = 'TablaJSON';
      } else if (typeof result.answer === 'string' && result.answer.trim() !== '' && result.answer !== 'Sin respuesta') {
        // If we have a text answer that's not empty or "Sin respuesta"
        responseContent = result.answer;
      } else if (result.status === 'success' && result.result) {
        // For other successful responses with a result
        responseContent = typeof result.result === 'string' ? result.result : JSON.stringify(result.result);
      } else {
        // No valid response content found
        console.log('No valid response content found in:', result);
        return null;
      }
      
      // Create a ChatMessage from the API response only if we have valid content
      if (responseContent) {
        const chatMessage: ChatMessage = {
          ID: Date.now(),
          RID: Date.now(),
          Usuario: userId,
          UsuarioDestino: '',
          Mensaje: message,
          FechaRegistro: new Date().toISOString(),
          MensajeTipo: messageType,
          Respuesta: responseContent
        };
        
        return chatMessage;
      }
      
      return null;
    } catch (parseError) {
      console.error('Error parsing message response:', parseError);
      console.error('Response text:', responseText);
      return null;
    }
  } catch (error) {
    console.error('Error sending message:', error);
    throw error;
  }
};
