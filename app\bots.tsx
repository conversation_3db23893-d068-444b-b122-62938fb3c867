import { FlatList, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { useState, useEffect } from 'react';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useAuth } from '@/context/AuthContext';
import { useChat } from '@/context/ChatContext';
import { ENDPOINTS } from '@/constants/Api';
import { setBotCategoryName } from '@/services/ChatService';

// Define the Bot type based on the API response
interface Bot {
  Bot: string;
  Nombre: string;
  FechaRegistro: string;
  Tipo: string;
  ID: number;
}

export default function BotsScreen() {
  const [bots, setBots] = useState<Bot[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { authData } = useAuth();
  const { fetchHistoryForBot, histories, loadingHistories, setCurrentBot } = useChat();

  useEffect(() => {
    fetchBots();
  }, []);

  const fetchBots = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!authData?.access_token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(ENDPOINTS.BOTS, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authData.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      const responseText = await response.text();
      console.log('Bots response status:', response.status);
      console.log('Bots response text:', responseText);

      if (!response.ok) {
        throw new Error(`Failed to fetch bots: ${response.status}`);
      }

      const data = JSON.parse(responseText);
      setBots(data);
      
      // Store bot category names in the map
      data.forEach((bot: Bot) => {
        setBotCategoryName(bot.ID, bot.Bot);
        console.log(`Stored bot category: ${bot.ID} -> ${bot.Bot}`);
      });
      
      // Start fetching history for each bot
      data.forEach((bot: Bot) => {
        fetchHistoryForBot(bot.ID);
      });
    } catch (e) {
      console.error('Error fetching bots:', e);
      setError(e instanceof Error ? e.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleBotPress = async (botId: number) => {
    // Set the current bot in the context
    setCurrentBot(botId);
    
    // Check if we have history for this bot
    const hasHistory = histories[botId] !== undefined;
    const isLoading = loadingHistories[botId];
    
    // If we don't have history yet and it's not loading, start fetching it
    if (!hasHistory && !isLoading) {
      try {
        console.log(`Fetching history for bot ${botId} before navigation`);
        await fetchHistoryForBot(botId);
      } catch (error) {
        console.error('Error pre-fetching history:', error);
        // Continue with navigation even if there's an error
      }
    }
    
    // Navigate to the chat screen
    router.push({
      pathname: '/chat/[id]',
      params: { 
        id: botId.toString()
      }
    });
  };

  if (loading) {
    return (
      <ThemedView style={styles.centerContainer}>
        <ActivityIndicator size="large" />
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centerContainer}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
        <TouchableOpacity style={styles.retryButton} onPress={fetchBots}>
          <ThemedText style={styles.retryButtonText}>Retry</ThemedText>
        </TouchableOpacity>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      {bots.length === 0 ? (
        <ThemedView style={styles.centerContainer}>
          <ThemedText>No bots available</ThemedText>
        </ThemedView>
      ) : (
        <FlatList
          data={bots}
          keyExtractor={(item) => item.ID.toString()}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.botCard}
              onPress={() => handleBotPress(item.ID)}
            >
              <ThemedText style={styles.avatar}>🤖</ThemedText>
              <ThemedView style={styles.botInfo}>
                <ThemedText type="subtitle">{item.Nombre}</ThemedText>
                <ThemedText>{item.Tipo}</ThemedText>
                <ThemedText style={styles.dateText}>
                  {new Date(item.FechaRegistro).toLocaleDateString()}
                </ThemedText>
              </ThemedView>
            </TouchableOpacity>
          )}
          contentContainerStyle={styles.listContent}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  listContent: {
    gap: 16,
  },
  botCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    gap: 16,
  },
  avatar: {
    fontSize: 32,
  },
  botInfo: {
    flex: 1,
    gap: 4,
  },
  dateText: {
    fontSize: 12,
    opacity: 0.7,
  },
  errorText: {
    color: '#ff6b6b',
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  retryButtonText: {
    fontWeight: 'bold',
  },
});