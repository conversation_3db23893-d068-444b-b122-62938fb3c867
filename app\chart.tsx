import React, { useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { DataChart } from '@/components/DataChart';
import { Stack, useLocalSearchParams } from 'expo-router';
import { useColorScheme } from '@/hooks/useColorScheme';

interface DataItem {
  [key: string]: string | number | Date;
}

export default function ChartScreen() {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { data: dataParam } = useLocalSearchParams();
  const [chartData, setChartData] = useState<DataItem[]>([]);

  useEffect(() => {
    if (dataParam) {
      try {
        const parsedData = JSON.parse(dataParam as string);
        setChartData(parsedData);
      } catch (error) {
        console.error('Error parsing data:', error);
      }
    }
  }, [dataParam]);

  return (
    <View style={[
      styles.container,
      { backgroundColor: isDark ? '#151718' : '#fff' }
    ]}>
      <Stack.Screen 
        options={{
          title: 'Visualización de Datos',
          headerLargeTitle: true,
        }} 
      />
      <DataChart data={chartData} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
}); 